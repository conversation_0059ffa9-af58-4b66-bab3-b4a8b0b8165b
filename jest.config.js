module.exports = {
  roots: ['<rootDir>/test'],
  transform: {
    '^.+\\.ts?$': [
      'ts-jest',
      {
        useESM: true,
      },
    ],
    '^.+\\.js$': [
      'babel-jest',
      {
        presets: [['@babel/preset-env', { targets: { node: 'current' } }]],
      },
    ],
  },
  // TODO: binary.test.ts 目前无法通过单测，需维护
  testPathIgnorePatterns: [
    '<rootDir>/test/binary.test.ts'
  ],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node', '.d.ts'],
  extensionsToTreatAsEsm: ['.ts', '.d.ts'],
  // Remove or comment out the moduleNameMapper configuration
  moduleNameMapper: {
    '^(.*)\\.js$': '$1',
    '^@/(.*)$': '<rootDir>/src/$1',
  },
};
