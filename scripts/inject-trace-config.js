const { writeFileSync } = require('fs');
const { resolve } = require('path');

const kconfKey = 'dmo.aidevops.kwaipilot-trace-config';
const defaultConfig = {};

async function injectConfig() {
  try {
    const res = await fetch(`https://team-robot.corp.kuaishou.com/api/team/kconf?key=${kconfKey}`);
    const config = res.ok ? (await res.json()).result : defaultConfig;

    // 追加到 .env 文件
    const envPath = resolve(process.cwd(), '.env.trace');
    const envContent = `\nTRACE_CONFIG=${JSON.stringify(config)}`;
    writeFileSync(envPath, envContent);
  } catch (error) {
    writeFileSync(resolve(process.cwd(), '.env'), `\nTRACE_CONFIG=${JSON.stringify(defaultConfig)}`);
  }
}

injectConfig();
