const esbuild = require('esbuild');
const fs = require('fs');
const path = require('path');
const ncp = require('ncp').ncp;
const packageJson = require('../package.json');
const version = packageJson.version;
const dotenv = require('dotenv');
const traceEnv = dotenv.config({ path: '.env.trace' }).parsed;

const { rimrafSync } = require('rimraf');
const {
  validateFilesPresent,
  downloadRipgrepBinary,
  execCmdSync,
  autodetectPlatformAndArch,
  uploadBinary
} = require('./util');

// Clean slate
const dist = path.join(__dirname, '..', 'dist');
const bin = path.join(dist, 'bin');
const out = path.join(dist, 'out');
const build = path.join(__dirname, '..', 'build');
rimrafSync(dist);
rimrafSync(build);
fs.mkdirSync(dist);
fs.mkdirSync(bin);
fs.mkdirSync(out);
fs.mkdirSync(build);
const esbuildOutputFile = path.join(out, 'index.js');
let targets = ['darwin-x64', 'darwin-arm64', 'linux-x64', 'linux-arm64', 'win32-x64'];

const [currentPlatform, currentArch] = autodetectPlatformAndArch();

const assetBackups = ['node_modules/win-ca/lib/crypt32-ia32.node.bak', 'node_modules/win-ca/lib/crypt32-x64.node.bak'];

let esbuildOnly = false;
for (let i = 2; i < process.argv.length; i++) {
  if (process.argv[i] === '--esbuild-only') {
    esbuildOnly = true;
  }
  if (process.argv[i - 1] === '--target') {
    targets = [process.argv[i]];
  }
}

const targetToLanceDb = {
  'darwin-arm64': '@lancedb/lancedb-darwin-arm64',
  'darwin-x64': '@lancedb/lancedb-darwin-x64',
  'linux-arm64': '@lancedb/lancedb-linux-arm64-gnu',
  'linux-x64': '@lancedb/lancedb-linux-x64-gnu',
  'win32-x64': '@lancedb/lancedb-win32-x64-msvc',
  'win32-arm64': '@lancedb/lancedb-win32-x64-msvc' // they don't have a win32-arm64 build
};
const targetToDotNodeName = {
  'darwin-arm64': 'lancedb.darwin-arm64.node',
  'darwin-x64': 'lancedb.darwin-x64.node',
  'linux-arm64': 'lancedb.linux-arm64-gnu.node',
  'linux-x64': 'lancedb.linux-x64-gnu.node',
  'win32-x64': 'lancedb.win32-x64-msvc.node',
  'win32-arm64': 'lancedb.win32-x64-msvc.node'
};
async function installNodeModuleInTempDirAndCopyToCurrent(packageName, toCopy) {
  console.log(`Copying ${packageName} to ${toCopy}`);
  // This is a way to install only one package without npm trying to install all the dependencies
  // Create a temporary directory for installing the package
  const adjustedName = packageName.replace(/@/g, '').replace('/', '-');
  const tempDir = path.join(dist, 'tmp', `kwaipilot-node_modules-${adjustedName}`);
  const currentDir = process.cwd();

  // // Remove the dir we will be copying to
  // rimrafSync(`node_modules/${toCopy}`);

  // // Ensure the temporary directory exists
  fs.mkdirSync(tempDir, { recursive: true });

  try {
    // Move to the temporary directory
    process.chdir(tempDir);

    // Initialize a new package.json and install the package
    execCmdSync(`npm init -y && npm i -f ${packageName} --no-save`);

    console.log(`Contents of: ${packageName}`, fs.readdirSync(path.join(tempDir, 'node_modules', toCopy)));

    // Without this it seems the file isn't completely written to disk
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Copy the installed package back to the current directory
    await new Promise((resolve, reject) => {
      ncp(
        path.join(tempDir, 'node_modules', toCopy),
        path.join(currentDir, 'node_modules', toCopy),
        { dereference: true },
        (error) => {
          if (error) {
            console.error(`[error] Error copying ${packageName} package`, error);
            reject(error);
          } else {
            resolve();
          }
        }
      );
    });
  } finally {
    // Clean up the temporary directory
    // rimrafSync(tempDir);

    // Return to the original directory
    process.chdir(currentDir);
  }
}

(async () => {
  fs.mkdirSync(path.join(dist, 'out', 'node_modules'), { recursive: true });
  fs.mkdirSync(path.join(dist, 'bin', 'node_modules'), { recursive: true });

  console.log('[info] Downloading prebuilt lancedb...');
  for (const target of targets) {
    if (targetToLanceDb[target]) {
      console.log(`[info] Downloading for ${target}...`);
      await installNodeModuleInTempDirAndCopyToCurrent(targetToLanceDb[target], '@lancedb');
    }
  }

  // tree-sitter-wasm
  const treeSitterWasmsDir = path.join(out, 'tree-sitter-wasms');
  fs.mkdirSync(treeSitterWasmsDir);
  await new Promise((resolve, reject) => {
    ncp(
      path.join(__dirname, '..', 'node_modules', 'tree-sitter-wasms', 'out'),
      treeSitterWasmsDir,
      { dereference: true },
      (error) => {
        if (error) {
          console.warn('[error] Error copying tree-sitter-wasm files', error);
          reject(error);
        } else {
          resolve();
        }
      }
    );
  });

  const filesToCopy = [
    './tree-sitter/vendor/tree-sitter.wasm'
    // '../core/llm/llamaTokenizerWorkerPool.mjs',
    // '../core/llm/llamaTokenizer.mjs',
    // '../core/llm/tiktokenWorkerPool.mjs',
  ];
  for (const f of filesToCopy) {
    fs.copyFileSync(path.join(__dirname, '..', 'src', f), path.join(dist, 'out', path.basename(f)));
    console.log(`[info] Copied ${path.basename(f)}`);
  }

  // Copy code-snippet-queries directory to dist/out
  const queriesSourceDir = path.join(__dirname, '..', 'src', 'tree-sitter', 'code-snippet-queries');
  const outQueriesTargetDir = path.join(dist, 'out', 'code-snippet-queries');
  fs.mkdirSync(outQueriesTargetDir, { recursive: true });
  await new Promise((resolve, reject) => {
    ncp(queriesSourceDir, outQueriesTargetDir, { dereference: true }, (error) => {
      if (error) {
        console.warn('[error] Error copying code-snippet-queries directory', error);
        reject(error);
      } else {
        console.log('[info] Copied code-snippet-queries directory');
        resolve();
      }
    });
  });

  console.log('[info] Cleaning up artifacts from previous builds...');

  // delete asset backups generated by previous pkg invocations, if present
  for (const assetPath of assetBackups) {
    fs.rmSync(assetPath, { force: true });
  }

  // Bundles the extension into one file
  console.log('[info] Building with esbuild...');
  const traceDefine = {};
  for (const k in traceEnv) {
    traceDefine[`process.env.${k}`] = JSON.stringify(traceEnv[k]);
  }
  await esbuild.build({
    entryPoints: ['src/index.ts'],
    bundle: true,
    outfile: esbuildOutputFile,
    external: [
      'esbuild',
      './xhr-sync-worker.js',
      'llamaTokenizerWorkerPool.mjs',
      'tiktokenWorkerPool.mjs',
      'vscode',
      './index.node'
    ],
    format: 'cjs',
    platform: 'node',
    sourcemap: true,
    loader: {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      '.node': 'file'
    },

    // To allow import.meta.path for transformers.js
    // https://github.com/evanw/esbuild/issues/1492#issuecomment-893144483
    inject: ['./importMetaUrl.js'],
    define: {
      ...traceDefine, // @IMP: 注入 生产环境 langfuse 的 key，生产环境必须这么做，否则 ，打包后的目录找不到 .env.trace 文件
      'import.meta.url': 'importMetaUrl'
    }
  });

  // Copy over any worker files
  fs.cpSync('./node_modules/jsdom/lib/jsdom/living/xhr/xhr-sync-worker.js', 'dist/out/xhr-sync-worker.js');
  // fs.cpSync('../core/llm/tiktokenWorkerPool.mjs', 'out/tiktokenWorkerPool.mjs');
  // fs.cpSync('../core/llm/llamaTokenizerWorkerPool.mjs', 'out/llamaTokenizerWorkerPool.mjs');

  if (esbuildOnly) {
    return;
  }

  console.log('[info] Building binaries with pkg...');
  for (const target of targets) {
    const targetDir = path.join(dist, 'bin', target);
    fs.mkdirSync(targetDir, { recursive: true });
    console.log(`[info] Building ${target}...`);
    await downloadRipgrepBinary(target, targetDir);
    execCmdSync(`npx pkg --no-bytecode --public-packages "*" --public pkgJson/${target} --out-path ${targetDir}`);

    // Download and unzip prebuilt sqlite3 binary for the target
    console.log('[info] Downloading node-sqlite3');
    const downloadUrl = `https://github.com/TryGhost/node-sqlite3/releases/download/v5.1.7/sqlite3-v5.1.7-napi-v6-${
      target === 'win32-arm64' ? 'win32-ia32' : target
    }.tar.gz`;
    execCmdSync(`curl -L -o ${targetDir}/build.tar.gz ${downloadUrl}`);
    execCmdSync(`cd ${targetDir} && tar -xvzf build.tar.gz`);

    // Informs of where to look for node_sqlite3.node https://www.npmjs.com/package/bindings#:~:text=The%20searching%20for,file%20is%20found
    fs.writeFileSync(
      `${targetDir}/package.json`,
      JSON.stringify(
        {
          name: 'binary',
          version: version,
          author: 'kwaipilot Dev, Inc',
          license: 'Apache-2.0'
        },
        undefined,
        2
      )
    );

    // Copy to build directory for testing
    try {
      const [platform, arch] = target.split('-');
      if (platform === currentPlatform && arch === currentArch) {
        fs.copyFileSync(`${targetDir}/build/Release/node_sqlite3.node`, `build/node_sqlite3.node`);
      }
    } catch (error) {
      console.log('[warn] Could not copy node_sqlite to build');
      console.log(error);
    }

    fs.unlinkSync(`${targetDir}/build.tar.gz`);

    // copy @lancedb to bin folders
    console.log('[info] Copying @lancedb files to bin');
    fs.copyFileSync(
      `node_modules/${targetToLanceDb[target]}/${targetToDotNodeName[target]}`,
      `${targetDir}/${targetToDotNodeName[target]}`
    );
  }

  const pathsToVerify = [];
  for (target of targets) {
    const exe = target.startsWith('win') ? '.exe' : '';
    const targetDir = path.join(dist, 'bin', target);
    pathsToVerify.push(
      `${targetDir}/kwaipilot-binary${exe}`,
      `${targetDir}/${targetToDotNodeName[target]}`, // @lancedb
      'package.json', // Informs of where to look for node_sqlite3.node https://www.npmjs.com/package/bindings#:~:text=The%20searching%20for,file%20is%20found
      `${targetDir}/build/Release/node_sqlite3.node`
    );
  }

  // Note that this doesn't verify they actually made it into the binary, just that they were in the expected folder before it was built
  pathsToVerify.push(path.join(out, 'index.js'));
  // pathsToVerify.push('out/llamaTokenizerWorkerPool.mjs');
  // pathsToVerify.push('out/tiktokenWorkerPool.mjs');
  // pathsToVerify.push('out/xhr-sync-worker.js');
  pathsToVerify.push(path.join(out, 'tree-sitter.wasm'));

  validateFilesPresent(pathsToVerify);

  console.log('[info] Done!');

  if (process.argv && process.argv[0] && process.argv[2] === 'upload') {
    // Package and upload dist/kwaipilot-binary to KCDN
    console.log('[info] Packaging and uploading to KCDN...');
    uploadBinary(dist);
  }
})();
