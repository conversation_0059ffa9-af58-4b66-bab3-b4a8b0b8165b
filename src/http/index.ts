import { Logger } from '@/util/log';
import {
  CloudCodeSearchBody,
  CloudCodeSearchResponse,
  CodeRerankBody,
  CodeRerankResponse,
  HttpClientResponse,
  QueryEmbeddingBody,
  QueryEmbeddingResponse
} from '@/http/types';
import { AGENT_NAMESPACE } from '@/util/const';
import { fetchEventSource, FetchEventSourceInit } from '@fortaine/fetch-event-source';
import { GlobalConfig } from '@/util/global';

interface HttpClientOptions {
  config?: RequestInit;
  requestInterceptors?: ((param: any) => any)[];
  responseInterceptors?: ((param: any) => any)[];
}

class HttpClient {
  private _config: RequestInit = {};
  private requestInterceptors: ((param: any) => any)[] = [];
  private responseInterceptors: ((param: any) => any)[] = [];
  logger = new Logger('http-client');

  get _baseUrl() {
    return GlobalConfig.getConfig().getKwaiPilotDomain();
  }

  constructor(options?: HttpClientOptions) {
    this._config = options?.config || {};
    this.requestInterceptors = options?.requestInterceptors || [];
    this.responseInterceptors = options?.responseInterceptors || [];
  }

  async request<R>(url: string, requestInit?: RequestInit) {
    const response = await this.rawRequest(url, requestInit);

    let responseData = (await response.json()) as R;

    this.responseInterceptors.forEach((interceptor) => {
      responseData = interceptor(responseData);
    });

    this.logger.debug('api response after interceptor');

    return responseData;
  }

  async rawRequest(url: string, requestInit?: RequestInit) {
    let config = this.mergeConfig(this._config, requestInit);
    config.headers = {
      'Content-Type': 'application/json',
      ...config.headers
    };
    config.method = config.method || 'GET';
    let response;
    let u = new URL(url, this._baseUrl);

    // qa环境下，这俩接口要走pre环境，因为服务端说qa缺少模型，不支持这俩接口
    if (
      this._baseUrl.includes('qa-kinsight.staging.kuaishou.com') &&
      (url.includes('local-agent-code-embedding') || url.includes('local-agent-code-rerank'))
    ) {
      u = new URL(url, 'https://pre-kinsight.test.gifshow.com/');
    }

    this.logger.debug(`api request url: ${u.toString()}`);
    this.requestInterceptors.forEach((interceptor) => {
      config = interceptor(config);
    });

    this.logger.debug('api request after interceptor');
    try {
      response = await fetch(u, config);
    } catch (error: any) {
      if (error.type === 'aborted') {
        throw 'user abort';
      }
      this.logger.error('api request error');
      throw new Error(error.toString());
    }

    if (!response.ok) {
      this.logger.error('api request error');
      throw new Error('An error occurred');
    }
    this.logger.info(`api request success: ${config.method} ${u.pathname} ${response.status}`);
    return response;
  }

  async get<T, R = HttpClientResponse<T>>(url: string, params: Record<string, string> = {}, config: RequestInit = {}) {
    const p = this.formatUrl(url, params);
    config.method = 'GET';
    return this.request<R>(p, config);
  }

  async post<T, R = HttpClientResponse<T>, D extends BodyInit = any>(url: string, body?: D, config: RequestInit = {}) {
    config.method = 'POST';
    config.body = body;
    return this.request<R>(url, config);
  }

  private mergeConfig(originConfig: RequestInit = {}, config: RequestInit = {}) {
    return { ...originConfig, ...config };
  }

  private formatUrl(url: string, params: Record<string, string>) {
    let isUrl = true;
    const urlObj = new URL(url, 'http://example.com');

    try {
      new URL(url);
    } catch (error) {
      isUrl = false;
    }

    // 获取现有的搜索参数
    const searchParams = urlObj.searchParams;

    // 遍历新的参数对象，并添加到搜索参数中
    for (const [key, value] of Object.entries(params)) {
      searchParams.append(key, value);
    }

    if (!isUrl) {
      return `${urlObj.pathname}${urlObj.search}`;
    }

    return urlObj.toString();
  }

  rawFetchEventSource(url: string, options: FetchEventSourceInit) {
    let u = null;
    if (typeof url === 'string') {
      u = new URL(url, this._baseUrl);
    }
    return fetchEventSource(u ? u.toString() : url, options);
  }
}

export class Api extends HttpClient {
  constructor(config?: HttpClientOptions) {
    super(config);
  }

  async queryEmbedding(body: QueryEmbeddingBody) {
    const startTime = Date.now();
    const { data } = await this.post<QueryEmbeddingResponse>(
      '/eapi/kwaipilot/plugin/code/search/local-agent-code-embedding',
      JSON.stringify(body)
    );
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'queryEmbeddingCost',
      millis: Date.now() - startTime
    });
    return data;
  }

  async codeRerank(body: CodeRerankBody) {
    const startTime = Date.now();
    const { data } = await this.post<CodeRerankResponse>(
      '/eapi/kwaipilot/plugin/code/search/local-agent-code-rerank',
      JSON.stringify(body)
    );
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'codeRerankCost',
      millis: Date.now() - startTime
    });
    return data;
  }

  async cloudCodeSearch(body: CloudCodeSearchBody) {
    const startTime = Date.now();
    const { data } = await this.post<CloudCodeSearchResponse>(
      '/eapi/kwaipilot/plugin/code/search/generate_prompt',
      JSON.stringify(body)
    );
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'cloudCodeSearchCost',
      millis: Date.now() - startTime
    });
    return data;
  }

  // todo: 这里是一个未实现的接口
  async getKconfValue<T>(key: string): Promise<T> {
    const response = await this.post<T>(
      `/eapi/kwaipilot/plugin/v2/config`,
      JSON.stringify({
        key
      })
    );
    return response.data;
  }

  async getLargeRepoIndexConfig(key: string): Promise<{ commitId: string; branch: string } | null> {
    const startTime = Date.now();
    const { data } = await this.get<{ commitId: string; branch: string } | null>(
      `/eapi/kwaipilot/plugin/code/search/large-repo-index-config`,
      { key }
    );
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'getLargeRepoIndexConfigCost',
      millis: Date.now() - startTime
    });
    return data;
  }

  fetchEventSource(url: string, options: FetchEventSourceInit): Promise<void> {
    return this.rawFetchEventSource(url, options);
  }
}
