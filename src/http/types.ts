export interface HttpClientResponse<T> {
  data: T;
  host: string;
  port: number;
  message: string;
  status: number;
  timestamp: string;
  traceId: string;
}

export interface QueryEmbeddingBody {
  chat_history: {
    content: string;
    role: string;
  }[];
  enable_rewrite: boolean;
  query: string;
}
export interface QueryEmbeddingResponse {
  code_embedding: number[];
  nl_embedding: number[];
  rerank_query: string;
  sparse_embedding: Record<string, number>;
}

export interface CodeRerankBody {
  code_context_list: {
    is_sub_node: boolean;
    code_content: string;
    metadata: {
      callees: string[];
      callers: string[];
      code_type: string;
      file_path: string;
      language: string;
      name: string;
      signature: string;
    };
    node_id: string;
    sub_node_id: number;
  }[];
  query: string;
  top_k: number;
}
export interface CodeRerankResponse {
  code_context_list: {
    _sub_node: boolean;
    code_content: string;
    metadata: {
      callees: string[];
      callers: string[];
      code_type: string;
      file_path: string;
      language: string;
      name: string;
      signature: string;
    };
    node_id: string;
    sub_node_id: number;
  }[];
}

export interface GeneratePromptFile {
  code: string;
  language: string;
  name: string;
}
export interface CloudCodeSearchBody {
  files: GeneratePromptFile[]; // 可选，不传就行
  searchTargetDirs: string[];
  query: string;
  codebaseSearch: boolean; // 一定要 true
  commit: string;
  repoName: string;
  username: string;
  instantApplyMode: boolean; // false 即可
  topK: number;
}

export interface CloudCodeSearchResponse {
  prompt: string;
  list: {
    id: number;
    path: string;
    startLineNo: number;
    endLineNo: number;
    startColNo: number;
    endColNo: number;
    code: string;
    language: string;
    repoName: string;
  }[];
}

export interface SearchParams {
  query: string;
  topK: number;
  targetDirectory: string[];
  chatHistory: any;
  enable_rewrite?: boolean;
  // 云端索引相关参数
  gitRepo?: string;
  commit?: string;
  username?: string;
  enableCloudSearch?: boolean;
}

export interface RetrieveDataItem {
  node_id: string;
  sub_node_id: number;
  is_sub_node: boolean;
  code_content: string;
  metadata: {
    name: string;
    signature: string;
    language: string;
    file_path: string;
    code_type: string;
    callers: string[];
    callees: string[];
  };
}
