/**
 * @description:
 * @Author: liuzhengzheng
 * @Date: 2025-03-22 19:37:08
 * @LastEditors: liuzhengzheng
 * @LastEditTime: 2025-03-24 15:28:22
 */
import { I<PERSON><PERSON><PERSON><PERSON>, IdeCommonMessage } from '@/protocol/messenger';
import { FromCoreProtocol, ToCoreProtocol } from '@/protocol';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { Logger } from '@/util/log';
import { GlobalConfig } from '@/util/global';
import { AgentManager } from '@/agent/agent';

/**
 * 处理助手代理相关消息的类
 */
export class AgentMessageHandler {
  private logger = new Logger('AgentMessageHandler');
  private _agentManager?: AgentManager;

  constructor(private readonly messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>) {}

  /**
   * 处理助手代理相关的消息
   * @param msg 消息对象
   */
  public async handleAgentMessage(msg: any): Promise<void> {
    this.logger.info(`assistant/agent/local, ${JSON.stringify(msg)}`);
    this.logger.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'assistant/agent/local',
      extra3: GlobalConfig.getConfig().getPlatform(),
      extra4: (msg.common as IdeCommonMessage)?.cwd,
      extra6: msg.data.type
    });

    const data = msg.data;
    const cwd = (msg.common as IdeCommonMessage)?.cwd || process.cwd();
    // const messageId = msg.messageId;

    switch (data.type) {
      // 如果是新任务开始，删除旧实例，新建一个实例
      case 'newTask': {
        this._agentManager = undefined;
        this._agentManager = await AgentManager.init(this.messenger, cwd, data);
        this._agentManager.startTask();
        break;
      }
      case 'stop': {
        this._agentManager?.stop();
        break;
      }
      case 'askResponse': {
        this._agentManager?.updateAskResponse({
          askResponse: data.askResponse,
          text: data.text
        });
        break;
      }
      case 'restore': {
        this._agentManager = undefined;
        this._agentManager = await AgentManager.init(this.messenger, cwd, undefined, data);
        await this._agentManager.checkpointResetHead(data.params.restoreCommitHash);
        break;
      }
      default:
        break;
    }
  }

  /**
   * 获取当前的代理管理器实例
   */
  public get agentManager(): AgentManager | undefined {
    return this._agentManager;
  }
}
