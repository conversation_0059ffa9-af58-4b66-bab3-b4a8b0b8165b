import Parser, { Language } from 'web-tree-sitter';
import * as path from 'node:path';
import fs from 'node:fs';
import { Logger } from '@/util/log';

const logger = new Logger('TreeSitter');

export enum LanguageName {
  CPP = 'cpp',
  C_SHARP = 'c_sharp',
  C = 'c',
  CSS = 'css',
  PHP = 'php',
  BASH = 'bash',
  JSON = 'json',
  TYPESCRIPT = 'typescript',
  TSX = 'tsx',
  ELM = 'elm',
  JAVASCRIPT = 'javascript',
  PYTHON = 'python',
  ELISP = 'elisp',
  ELIXIR = 'elixir',
  GO = 'go',
  EMBEDDED_TEMPLATE = 'embedded_template',
  HTML = 'html',
  JAVA = 'java',
  LUA = 'lua',
  OCAML = 'ocaml',
  QL = 'ql',
  RESCRIPT = 'rescript',
  RUBY = 'ruby',
  RUST = 'rust',
  SYSTEMRDL = 'systemrdl',
  TOML = 'toml',
  SOLIDITY = 'solidity'
}
export const supportedLanguages: { [key: string]: LanguageName } = {
  cpp: LanguageName.CPP,
  hpp: LanguageName.CPP,
  cc: LanguageName.CPP,
  cxx: LanguageName.CPP,
  hxx: LanguageName.CPP,
  cp: LanguageName.CPP,
  hh: LanguageName.CPP,
  inc: LanguageName.CPP,
  // Depended on this PR: https://github.com/tree-sitter/tree-sitter-cpp/pull/173
  // ccm: LanguageName.CPP,
  // c++m: LanguageName.CPP,
  // cppm: LanguageName.CPP,
  // cxxm: LanguageName.CPP,
  cs: LanguageName.C_SHARP,
  c: LanguageName.C,
  h: LanguageName.C,
  css: LanguageName.CSS,
  php: LanguageName.PHP,
  phtml: LanguageName.PHP,
  php3: LanguageName.PHP,
  php4: LanguageName.PHP,
  php5: LanguageName.PHP,
  php7: LanguageName.PHP,
  phps: LanguageName.PHP,
  'php-s': LanguageName.PHP,
  bash: LanguageName.BASH,
  sh: LanguageName.BASH,
  json: LanguageName.JSON,
  ts: LanguageName.TYPESCRIPT,
  mts: LanguageName.TYPESCRIPT,
  cts: LanguageName.TYPESCRIPT,
  tsx: LanguageName.TSX,
  // vue: LanguageName.VUE,  // tree-sitter-vue parser is broken
  // The .wasm file being used is faulty, and yaml is split line-by-line anyway for the most part
  // yaml: LanguageName.YAML,
  // yml: LanguageName.YAML,
  elm: LanguageName.ELM,
  js: LanguageName.JAVASCRIPT,
  jsx: LanguageName.JAVASCRIPT,
  mjs: LanguageName.JAVASCRIPT,
  cjs: LanguageName.JAVASCRIPT,
  py: LanguageName.PYTHON,
  ipynb: LanguageName.PYTHON,
  pyw: LanguageName.PYTHON,
  pyi: LanguageName.PYTHON,
  el: LanguageName.ELISP,
  emacs: LanguageName.ELISP,
  ex: LanguageName.ELIXIR,
  exs: LanguageName.ELIXIR,
  go: LanguageName.GO,
  eex: LanguageName.EMBEDDED_TEMPLATE,
  heex: LanguageName.EMBEDDED_TEMPLATE,
  leex: LanguageName.EMBEDDED_TEMPLATE,
  html: LanguageName.HTML,
  htm: LanguageName.HTML,
  java: LanguageName.JAVA,
  lua: LanguageName.LUA,
  ocaml: LanguageName.OCAML,
  ml: LanguageName.OCAML,
  mli: LanguageName.OCAML,
  ql: LanguageName.QL,
  res: LanguageName.RESCRIPT,
  resi: LanguageName.RESCRIPT,
  rb: LanguageName.RUBY,
  erb: LanguageName.RUBY,
  rs: LanguageName.RUST,
  rdl: LanguageName.SYSTEMRDL,
  toml: LanguageName.TOML,
  sol: LanguageName.SOLIDITY

  // jl: LanguageName.JULIA,
  // swift: LanguageName.SWIFT,
  // kt: LanguageName.KOTLIN,
  // scala: LanguageName.SCALA,
};
const nameToLanguage = new Map<string, Language>();
const nameToParser = new Map<string, Parser>();

export async function getParserForFile(lang: string) {
  try {
    await Parser.init();

    let parser = nameToParser.get(lang);
    if (!parser) {
      parser = new Parser();
      const language = await getLanguageForFile(lang);
      parser.setLanguage(language);
      nameToParser.set(lang, parser);
    }
    return parser;
  } catch (error) {
    logger.error(typeof error === 'string' ? error : JSON.stringify(error));
    throw error;
  }
}
// Loading the wasm files to create a Language object is an expensive operation and with
// sufficient number of files can result in errors, instead keep a map of language name
// to Language object
export async function getLanguageForFile(lang: string): Promise<Language | undefined> {
  try {
    if (!lang) {
      return undefined;
    }
    let language = nameToLanguage.get(lang);

    if (!language) {
      language = await loadLanguageForFileExt(lang);
      logger.info(`load language wasm successfully ${lang}`);
      nameToLanguage.set(lang, language);
    }
    return language;
  } catch (e) {
    logger.error(`Unable to load language for file ${lang}, ${e}`);
    return undefined;
  }
}
export const getFullLanguageName = (filepath: string) => {
  return supportedLanguages[filepath.split('.').pop() ?? ''];
};

async function loadLanguageForFileExt(lang: string): Promise<Language> {
  logger.info(`dirname ${__dirname}`);
  const wasmPath = path.join(
    __dirname,
    ...(process.env.NODE_ENV === 'test'
      ? ['..', '..', 'node_modules', 'tree-sitter-wasms', 'out']
      : ['tree-sitter-wasms']),
    `tree-sitter-${lang}.wasm`
  );
  logger.info(`wasmPath ${wasmPath}`);
  return await Parser.Language.load(wasmPath);
}

export async function getQueryForFile(lang: string): Promise<Parser.Query | undefined> {
  try {
    const language = await getLanguageForFile(lang);
    if (!language) {
      return undefined;
    }
    const queryPath = `code-snippet-queries/${lang}.scm`;

    const sourcePath = path.join(
      __dirname,
      ...(process.env.NODE_ENV === 'test' ? ['..', 'tree-sitter'] : ['']),
      queryPath
    );
    if (!fs.existsSync(sourcePath)) {
      logger.error(`Query file not found at path: ${sourcePath}`);
      return undefined;
    }
    const querySource = fs.readFileSync(sourcePath).toString();

    try {
      const query = language.query(querySource);
      return query;
    } catch (queryError) {
      logger.error(`Error creating query for language ${lang}:`, queryError);
      return undefined;
    }
  } catch (error) {
    logger.error(`Error in getQueryForFile for language ${lang}:`, error);
    return undefined;
  }
}
