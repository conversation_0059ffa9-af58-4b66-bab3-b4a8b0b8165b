(function_signature
  name: (identifier) @name.definition.function) @definition.function

(method_signature
  name: (property_identifier) @name.definition.method) @definition.method

(abstract_method_signature
  name: (property_identifier) @name.definition.method) @definition.method

(abstract_class_declaration
  name: (type_identifier) @name.definition.class) @definition.class

(module
  name: (identifier) @name.definition.module) @definition.module

(interface_declaration
  name: (type_identifier) @name.definition.interface) @definition.interface

(type_annotation
  (type_identifier) @name.reference.type) @reference.type

(new_expression
  constructor: (identifier) @name.reference.class) @reference.class

(function_declaration
  name: (identifier) @name.definition.function) @definition.function

(method_definition
  name: (property_identifier) @name.definition.method) @definition.method

(class_declaration
  name: (type_identifier) @name.definition.class) @definition.class

(interface_declaration
  name: (type_identifier) @name.definition.class) @definition.class

(type_alias_declaration
  name: (type_identifier) @name.definition.type) @definition.type

(enum_declaration
  name: (identifier) @name.definition.enum) @definition.enum

(
  (comment)* @doc
  .
  (lexical_declaration
    (variable_declarator
      name: (identifier) @name.definition.function
      value: [(arrow_function) (function_expression)])) @definition.function
  (#strip! @doc "^[\\s\\*/]+|^[\\s\\*/]$")
  (#select-adjacent! @doc @definition.function)
)

(
  (comment)* @doc
  .
  (export_statement
    value: [(arrow_function) (function_expression)]) @definition.function
)

(
  (comment)* @doc
  .
  (variable_declaration
    (variable_declarator
      name: (identifier) @name.definition.function
      value: [(arrow_function) (function_expression)])) @definition.function
  (#strip! @doc "^[\\s\\*/]+|^[\\s\\*/]$")
  (#select-adjacent! @doc @definition.function)
)

(assignment_expression
  left: [
    (identifier) @name.definition.function
    (member_expression
      property: (property_identifier) @name.definition.function)
  ]
  right: [(arrow_function) (function_expression)]
) @definition.function

(pair
  key: (property_identifier) @name.definition.function
  value: [(arrow_function) (function_expression)]) @definition.function

(
  (call_expression
    function: (identifier) @name.reference.call) @reference.call
  (#not-match? @name.reference.call "^(require)$")
)

(call_expression
  function: (member_expression
    property: (property_identifier) @name.reference.call)
  arguments: (_) @reference.call)

(
  (arguments (arrow_function))
) @definition.anonymous.function

(program
  (lexical_declaration (
    (variable_declarator
      name: (identifier) @name.definition.variable.global
      value: [(object) (array) (string) (call_expression)]
    ) @definition.variable.global)
  )
)

(program
  (export_statement (
  lexical_declaration (
    (variable_declarator
      name: (identifier) @name.definition.variable.global
      value: [(object) (array) (string) (call_expression)]
    ))
  )) @definition.variable.global
)

(program
  (enum_declaration
    name: (identifier) @name.definition.variable.global
  ) @definition.variable.global
)

(program
  (export_statement
    (enum_declaration
      name: (identifier) @name.definition.variable.global
    ) @definition.variable.global
  )
)

(variable_declarator
  name: (identifier) @name.definition.variable
  value: [(object) (array) (string) (call_expression)]
)

(enum_declaration
  name: (identifier) @name.definition.variable
)

(assignment_expression
  left: [
    (identifier) @name.definition.variable
  ]
  right: [(object) (array) (string) (call_expression)]
)