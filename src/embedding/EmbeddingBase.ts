import { AutoTokenizer } from '@/code/chunk/Tokenlizer';
import * as lance from '@lancedb/lancedb';
import { AGENT_NAMESPACE, code_dense_dim, nl_dense_dim } from '@/util/const';
import { LanceDB } from '@/db/lancedb';
import { Logger } from '@/util/log';
import { GlobalConfig } from '@/util/global';
const logger = new Logger('EmbeddingBase');

export class EmbeddingBase {
  tokenizer: AutoTokenizer;
  code_dense_dim: number = code_dense_dim;
  nl_dense_dim: number = nl_dense_dim;
  constructor() {
    this.tokenizer = AutoTokenizer.from_pretrained();
    this.initializeModel();
  }

  private async constructRequiredData(dataInput: any[], failIds: Set<any>): Promise<Record<string, unknown>[]> {
    let retryCount = 3;
    let isDone = false;

    while (!isDone && retryCount >= 0) {
      try {
        dataInput = await this.constructData(dataInput);
        isDone = true;
      } catch (e) {
        console.error('Construct Data Error:', e);
        console.trace();
        console.log(`Remaining attempts: ${retryCount}`);
        retryCount--;
      }
    }

    if (!isDone) {
      this.recordFails(dataInput, failIds);
    }
    return dataInput;
  }
  // 删除数据库，清除所有数据
  async clearIndex(collectionName: string) {
    const lanceDb = await LanceDB.get();
    await lanceDb.dropTable(collectionName);
  }
  async insertData(collectionName: string, data: any[]): Promise<any[]> {
    const failIds = new Set();

    // Validate data before processing
    const validateVectors = (d: Record<string, unknown>) => {
      const codeVector = d.code_vector as number[];
      const nlVector = d.nl_vector as number[];
      if (!Array.isArray(codeVector) || codeVector.length !== this.code_dense_dim) {
        throw new Error(`Invalid code_vector dimension. Expected ${this.code_dense_dim}, got ${codeVector?.length}`);
      }
      if (!Array.isArray(nlVector) || nlVector.length !== this.nl_dense_dim) {
        throw new Error(`Invalid nl_vector dimension. Expected ${this.nl_dense_dim}, got ${nlVector?.length}`);
      }
    };

    const batches: any[][] = [];
    let batchTokenLen = 0;
    let currentBatch: any[] = [];

    // Create batches based on token length
    for (const d of data) {
      const dTokenLen = this.getDataTokenLength(d);
      if (batchTokenLen + dTokenLen >= 10000) {
        batches.push(currentBatch);
        currentBatch = [];
        batchTokenLen = 0;
      }
      currentBatch.push(d);
      batchTokenLen += dTokenLen;
    }
    if (currentBatch.length > 0) {
      batches.push(currentBatch);
    }

    // Process batches sequentially
    for (const batch of batches) {
      try {
        let startTime = Date.now();

        const constructedData = await this.constructRequiredData(batch, failIds);
        logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'INDEXING_constructRequiredData',
          millis: Date.now() - startTime,
          extra4: batch.length + '',
          extra6: 'success'
        });
        logger.reportUserAction({
          key: 'INDEXING',
          type: 'constructRequiredData',
          subType: 'success',
          content: JSON.stringify({
            repo_dir: GlobalConfig.getConfig().getRepoPath(),
            time: Date.now() - startTime,
            data_length: batch.length
          })
        });
        startTime = Date.now();
        // Validate each record before insertion
        constructedData.forEach(validateVectors);
        await LanceDB.insertData(collectionName, constructedData as any);
        logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'INDEXING_insertDataLanceDB',
          millis: Date.now() - startTime,
          extra4: batch.length + '',
          extra6: 'success'
        });
        logger.reportUserAction({
          key: 'INDEXING',
          type: 'insertDataLanceDB',
          subType: 'success',
          content: JSON.stringify({
            repo_dir: GlobalConfig.getConfig().getRepoPath(),
            time: Date.now() - startTime,
            data_length: batch.length
          })
        });
      } catch (e) {
        logger.error('LanceDB insert error:', e);
        this.recordFails(batch, failIds);
        console.trace();
        throw e;
      }
    }

    await LanceDB.closeTable(collectionName);
    return Array.from(failIds);
  }

  // Abstract methods to be implemented by child classes
  protected getDataTokenLength(data: any): number {
    throw new Error('Method not implemented');
  }

  protected recordFails(data: any[], failIds: Set<any>): void {
    throw new Error('Method not implemented');
  }

  protected deleteDuplicateData(client: lance.Connection, collectionName: string, data: any[]): Promise<void> {
    throw new Error('Method not implemented');
  }

  protected initProgressTracker(request: any, data: any[]): void {
    // Implementation optional
  }

  protected constructData(datas: any[]): Promise<Record<string, unknown>[]> {
    throw new Error('Method not implemented');
  }

  protected initializeModel(): void {
    throw new Error('Method not implemented');
  }
}
