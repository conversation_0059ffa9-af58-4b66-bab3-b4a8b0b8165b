import { AGENT_NAMESPACE } from '@/util/const';
import { GlobalConfig } from '@/util/global';
import { Logger } from '@/util/log';

export enum EmbeddingServiceName {
  oasis = 'grpc_python_kwaipilot_oasis',
  bgem3 = 'grpc_python_kwaipilot_bgem3',
  sparse = 'grpc_python_kwaipilot_llm_api_sparse_embedding',
}
export class EmbeddingModel {
  private serviceName: string;
  private logger = new Logger('EmbeddingModel');

  constructor(serviceName: string) {
    this.serviceName = serviceName;
  }

  async getEmbedding(texts: string[]): Promise<any[] | null> {
    const startTime = Date.now();
    const KwaiPilotDomain = GlobalConfig.getConfig().getKwaiPilotDomain();
    const url = `${KwaiPilotDomain}eapi/kwaipilot/plugin/code/search/local-agent-batch-embedding`;
    const payload = {
      text: '11',
      texts,
      serviceName: this.serviceName,
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(20000),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'embedding_model_get_embedding_cost',
        millis: Date.now() - startTime,
        extra5: this.serviceName,
        extra6: 'success',
      });
      const data = await response.json();
      return data.data;
    } catch (error) {
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'embedding_model_get_embedding_cost',
        millis: Date.now() - startTime,
        extra5: this.serviceName,
        extra6: 'failed',
      });
      console.error('Request failed:', error, 'INPUT:', payload);
      throw error;
    }
  }
}
