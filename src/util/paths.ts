import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import dotenv from 'dotenv';
import crypto from 'crypto';
import { GlobalConfig } from './global';

dotenv.config();
const KWAIPILOT_GLOBAL_DIR = process.env.KWAIPILOT_GLOBAL_DIR ?? path.join(os.homedir(), '.kwaipilot');

export const md5 = (str: string) => {
  return crypto.createHash('md5').update(str).digest('hex');
};

export const getKwaipilotGlobalPath = (): string => {
  // This is ~/.kwaipilot on mac/linux
  const kwaipilotPath = KWAIPILOT_GLOBAL_DIR;
  if (!fs.existsSync(kwaipilotPath)) {
    fs.mkdirSync(kwaipilotPath, { recursive: true });
  }
  return kwaipilotPath;
};

export function getIndexFolderPath(): string {
  const indexPath = path.join(getKwaipilotGlobalPath(), 'index');
  if (!fs.existsSync(indexPath)) {
    fs.mkdirSync(indexPath, { recursive: true });
  }
  return indexPath;
}

export function getSqlitePath(repoPath: string) {
  const sqlitePath = path.join(getIndexFolderPath(), 'sqlite', md5(repoPath));
  if (!fs.existsSync(sqlitePath)) {
    fs.mkdirSync(sqlitePath, { recursive: true });
  }
  return sqlitePath;
}
// new sqlite path
export function getIndexSqlitePath(): string {
  const dirPath = GlobalConfig.getConfig().getRepoPath();
  return path.join(getSqlitePath(dirPath), 'index.sqlite');
}

export function getOriginalIndexSqlitePath(): string {
  return path.join(getIndexFolderPath(), 'index.sqlite');
}
export function getTmpSqlitePath(repoPath: string): string {
  return path.join(getSqlitePath(repoPath), 'tmp.sqlite');
}

export function getLanceDbPath(): string {
  return path.join(getIndexFolderPath(), 'lancedb');
}

export function getDocsSqlitePath(): string {
  return path.join(getIndexFolderPath(), 'docs.sqlite');
}

export function getLogsDirPath(): string {
  const logsPath = path.join(getKwaipilotGlobalPath(), 'logs');
  if (!fs.existsSync(logsPath)) {
    fs.mkdirSync(logsPath, { recursive: true });
  }
  return logsPath;
}

export function getCoreLogsPath(): string {
  return path.join(getLogsDirPath(), 'core.log');
}
export function getErrorLogsPath(): string {
  return path.join(getLogsDirPath(), 'error.log');
}

export function getMcpDirPath(): string {
  const mcpPath = path.join(getKwaipilotGlobalPath(), 'mcp');
  if (!fs.existsSync(mcpPath)) {
    fs.mkdirSync(mcpPath, { recursive: true });
  }
  return mcpPath;
}

export function getMcpSettingsPath(): string {
  return path.join(getMcpDirPath(), 'kwaipilot-mcp-settings.json');
}

export function getMcpConfigPath(): string {
  return path.join(getMcpDirPath(), 'kwaipilot-mcp-config.json');
}
