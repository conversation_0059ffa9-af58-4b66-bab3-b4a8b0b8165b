import Langfuse, { type LangfuseTraceClient } from 'langfuse';
import { hostname } from 'os';
import { config } from 'dotenv';
import { IS_DEV } from './const';
// @IMP: 生产环境在 build.js 中注入 .env.trace 文件 防止打包执行找不到 .env.trace 文件
IS_DEV  && config({ path: '.env.trace' });

let langfuse: Langfuse;
const env = IS_DEV ? 'development' : 'production';

interface LangfuseConfig {
  secretKey: string;
  publicKey: string;
  baseUrl: string;
}
const traceConfig: LangfuseConfig = JSON.parse(process.env.TRACE_CONFIG || '{}');

const getLangfuse = () => {
  if (langfuse) {
    return langfuse;
  }

  langfuse = new Langfuse(traceConfig);
  return langfuse;
};

const getTrace = (
  id?: string | null,
  username?: string,
  options?: Partial<Parameters<Langfuse['trace']>[0]>
): LangfuseTraceClient => {
  return getLangfuse().trace({
    ...options,
    name: 'kwaipilot-agent',
    userId: username,
    tags: [env, hostname()],
    id
  });
};

export { getTrace };
