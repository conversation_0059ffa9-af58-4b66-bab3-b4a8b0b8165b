/**
   * 将各种格式的 git 仓库地址转换为标准的 git SSH 格式
   * @param gitRepo 原始 git 仓库地址
   * @returns 标准的 git SSH 格式地址，如: *************************:path/to/repo.git
   */
  export const convertToGitSshFormat =  function convertToGitSshFormat(gitRepo: string): string | null {
    try {
      // 如果已经是 git SSH 格式，直接返回
      if (gitRepo.startsWith('git@')) {
        // 确保以 .git 结尾
        return gitRepo.endsWith('.git') ? gitRepo : `${gitRepo}.git`;
      }
      
      // 处理 http/https 格式
      if (gitRepo.startsWith('http://') || gitRepo.startsWith('https://')) {
        const url = new URL(gitRepo);
        let path = url.pathname.substring(1); // 移除开头的 /
        
        // 确保以 .git 结尾
        if (!path.endsWith('.git')) {
          path = `${path}.git`;
        }
        
        return `git@${url.hostname}:${path}`;
      }
      
      // 处理 git:// 格式
      if (gitRepo.startsWith('git://')) {
        const url = new URL(gitRepo);
        let path = url.pathname.substring(1); // 移除开头的 /
        
        // 确保以 .git 结尾
        if (!path.endsWith('.git')) {
          path = `${path}.git`;
        }
        
        return `git@${url.hostname}:${path}`;
      }
      
      return null;
    } catch (error) {
      return null;
    }
  } 