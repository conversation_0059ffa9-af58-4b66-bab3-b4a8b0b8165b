import fs from 'fs/promises';
import crypto from 'crypto';
import path from 'path';
import { existsSync, readFileSync } from 'fs';
// 获取文件 hash 值
export const getFileMd5 = async (filePath: string) => {
  const content = await fs.readFile(filePath);
  return crypto.createHash('md5').update(content).digest('hex');
};
export const getKwaipilotIgnoreFiles = (dirPath: string): string[] => {
  const indexIgnorePath = path.join(dirPath, '.kwaipilot', '.indexignore');
  if (existsSync(indexIgnorePath)) {
    const content = readFileSync(indexIgnorePath, 'utf-8');
    return content.split('\n').filter((line) => line.trim() !== '');
  }
  return [];
};
