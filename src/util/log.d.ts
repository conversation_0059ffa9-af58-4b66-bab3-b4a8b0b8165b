export interface PerfInfo {
  namespace: string; // 用来唯一标识 vscode 插件
  subtag: string;
  millis?: number;
  extra3?: string;
  extra4?: string;
  extra5?: string;
  extra6?: string;
}
export interface PerformanceLog {
  namespace: string; // 用来唯一标识 vscode 插件
  stage: string; // 区分功能场景，如续写、问答
  duration: number; // 耗时
}

export interface StableLog {
  namespace: string; // 用来唯一标识 vscode 插件
  stage: string; // 区分功能场景，如续写、问答
  extra1: string; // 用来计量，比如使用'count'
  extra2?: string; // 用来区分成功还是失败，比如 'error' | 'success'
}
