import { Field, Schema, Int64, Int32, Float32, Utf8, FixedSizeList, List } from 'apache-arrow';

import { connect, Connection, Query, VectorQuery, Table, Index } from '@lancedb/lancedb';
import { ContextNode } from '@/code/node/ContextNode';
import { getLanceDbPath } from '@/util/paths';
import { NodeGraphTable, NodeGraphType } from '@/db/sqlite/tables/node-graph';
import { code_dense_dim, getCacheCollectionName, getNonCodeCollectionName, nl_dense_dim } from '@/util/const';
import fs from 'fs';
import { Logger } from '@/util/log';

const getSchema = (isCode: boolean): Schema => {
  return new Schema([
    new Field('id', new Utf8()),
    new Field('node_id', new Utf8()),
    new Field('sub_id', new Utf8()),
    new Field('is_sub_node', new Utf8()),
    new Field('file_name', new Utf8()),
    new Field('file_path', new Utf8()),
    new Field('code_type', new Utf8()),
    new Field('name', new Utf8()),
    new Field('signature', new Utf8()),
    new Field('line', new Int32()),
    new Field('line_from', new Int32()),
    new Field('line_to', new Int32()),
    new Field(
      'code_vector',
      new FixedSizeList(isCode ? code_dense_dim : nl_dense_dim, new Field('item', new Float32()))
    ),
    new Field('nl_vector', new FixedSizeList(nl_dense_dim, new Field('item', new Float32()))),
    new Field('sparse_vector_indices', new List(new Field('item', new Int64()))),
    new Field('sparse_vector_values', new List(new Field('item', new Float32()))),
    new Field('snippet', new Utf8()),
    new Field('language', new Utf8()),
    new Field('caller_references', new List(new Field('item', new Utf8()))), // 存的 nodeid
    new Field('callee_references', new List(new Field('item', new Utf8()))), // 存的 nodeid
    new Field('def_parent_functions', new List(new Field('item', new Utf8()))), // 存的 nodeid
    new Field('def_child_functions', new List(new Field('item', new Utf8()))) // 存的 nodeid
  ]);
};
export class LanceDB {
  static db: Connection | null = null;
  static cacheTable: Table | null = null;
  static codeTable: Table | null = null;
  private static logger = new Logger('LanceDB');
  private static async initDb(db: Connection) {
    try {
      // Ensure the database connection is valid
      await db.tableNames();
      console.log('Database connection initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }
  static closeTable = async (collectionName: string) => {
    const table = await LanceDB.getTable(collectionName);
    await table.close();
  };

  static async get(): Promise<Connection> {
    if (LanceDB.db) {
      return LanceDB.db;
    }
    try {
      const path = getLanceDbPath();
      this.logger.info(`lanceDb path ----- ${path}`);
      // Ensure the directory exists
      if (!fs.existsSync(path)) {
        fs.mkdirSync(path, { recursive: true });
      }
      // 列出当前文件夹 cwd
      console.log('当前文件夹 ----- ', process.cwd());
      // 列出当前文件夹下内容
      try {
        LanceDB.db = await connect(path);
        console.log('after connect');
      } catch (e) {
        const connectError = e as Error;
        console.error('LanceDB connect error details:', {
          error: connectError,
          errorMessage: connectError.message,
          errorStack: connectError.stack,
          path: path,
          pathExists: fs.existsSync(path),
          pathStats: fs.existsSync(path) ? fs.statSync(path) : null
        });
        throw connectError;
      }
      await LanceDB.initDb(LanceDB.db);
    } catch (error) {
      console.error('initDb failed, 初始化数据库失败', error);
      throw error;
    }
    return LanceDB.db;
  }
  static async getCacheTable(collectionName: string): Promise<Table> {
    if (LanceDB.cacheTable && LanceDB.cacheTable.isOpen()) {
      return LanceDB.cacheTable;
    }
    const cacheCollectionName = getCacheCollectionName(collectionName);
    LanceDB.cacheTable = await this.getTable(cacheCollectionName);
    return LanceDB.cacheTable;
  }
  static async getCodeTable(collectionName: string): Promise<Table> {
    // Always get a fresh table instance to ensure data consistency
    return await this.getTable(collectionName);
  }

  async getTable(collectionName: string) {
    let table: Table | null = null;
    const db = await LanceDB.get();
    const exists = (await db.tableNames()).includes(collectionName);
    if (!exists) {
      table = await db.createTable(collectionName, [], {
        schema: getSchema(true)
      });
    } else {
      table = await db.openTable(collectionName);
    }
    return table;
  }

  static createDbIndex = async (table: Table) => {
    // 检查表中是否有数据
    const rowCount = await table.countRows();
    this.logger.info(`表 ${table.name} 数据量: ${rowCount}`);
    if (rowCount === 0) {
      this.logger.info(`表中没有数据，跳过索引创建 ${table.name}`);
      return;
    }

    // 获取现有索引
    const existingIndexes = await table.listIndices();
    const indexNames = existingIndexes.map((index) => index.name);
    this.logger.info(`表 ${table.name} 现有索引: ${indexNames.join(', ')}`);

    // 为标量列创建索引（如果不存在）
    if (!indexNames.includes('file_path_idx')) {
      await table.createIndex('file_path', {
        config: Index.btree()
      });
      this.logger.info(`创建 file_path 索引完成`);
    }

    if (!indexNames.includes('name_idx')) {
      await table.createIndex('name', {
        config: Index.btree()
      });
      this.logger.info(`创建 name 索引完成`);
    }

    if (!indexNames.includes('file_name_idx')) {
      await table.createIndex('file_name', {
        config: Index.btree()
      });
      this.logger.info(`创建 file_name 索引完成`);
    }

    this.logger.info('索引检查和创建完成！');
  };
  static initCollection = async (collectionName: string) => {
    let table: Table | null = null;
    const db = await LanceDB.get();
    const tableExists = (await db.tableNames()).includes(collectionName);
    if (!tableExists) {
      const data: Record<string, unknown>[] = [];
      table = await db.createTable(collectionName, data, {
        schema: getSchema(true)
      });
    } else {
      table = await db.openTable(collectionName);
    }
    return table;
  };

  getCacheTable = async (collectionName: string) => {
    const db = await LanceDB.get();
    const table = await db.openTable(getCacheCollectionName(collectionName));
    return table;
  };
  static getTable = async (collectionName: string) => {
    return await LanceDB.initCollection(collectionName);
  };
  // 插入完成之后，需要重新索引一下
  static optimizeTable = async (collectionName: string) => {
    const table = await LanceDB.getCodeTable(collectionName);

    try {
      // 获取现有索引
      const existingIndexes = await table.listIndices();
      const indexNames = existingIndexes.map((index) => index.name);
      this.logger.info(`表 ${table.name} 现有索引: ${indexNames.join(', ')}`);

      // 删除向量索引
      try {
        if (indexNames.includes('code_vector_idx')) {
          await table.dropIndex('code_vector_idx');
          this.logger.info(`删除 code_vector 索引完成`);
        }
      } catch (error) {
        this.logger.error(`删除 code_vector 索引失败: ${error}`);
      }

      try {
        if (indexNames.includes('nl_vector_idx')) {
          await table.dropIndex('nl_vector_idx');
          this.logger.info(`删除 nl_vector 索引完成`);
        }
      } catch (error) {
        this.logger.error(`删除 nl_vector 索引失败: ${error}`);
      }

      // 执行优化
      try {
        await table.optimize();
        this.logger.info(`表 ${table.name} 优化完成`);
      } catch (error) {
        this.logger.error(`表 ${table.name} 优化失败: ${error}`);
        throw error; // 优化失败是关键错误，需要抛出
      }

      // 重新创建向量索引
      try {
        const rowCount = await table.countRows();
        if (rowCount >= 5000) {
          // 创建 code_vector 索引
          try {
            await table.createIndex('code_vector', {
              config: Index.ivfPq({
                numPartitions: 128,
                numSubVectors: 16
              })
            });
            this.logger.info(`重新创建 code_vector 索引完成`);
          } catch (indexError) {
            this.logger.error(`重新创建 code_vector 索引失败: ${indexError}`);
          }

          // 创建 nl_vector 索引
          try {
            await table.createIndex('nl_vector', {
              config: Index.ivfPq({
                numPartitions: 128,
                numSubVectors: 16
              })
            });
            this.logger.info(`重新创建 nl_vector 索引完成`);
          } catch (indexError) {
            this.logger.error(`重新创建 nl_vector 索引失败: ${indexError}`);
          }
        }
      } catch (error) {
        this.logger.error(`获取表行数或创建索引失败: ${error}`);
      }
    } catch (error) {
      this.logger.error(`表 ${table.name} 优化过程中发生错误: ${error}`);
    } finally {
      try {
        await table.close();
      } catch (error) {
        this.logger.error(`关闭表 ${table.name} 失败: ${error}`);
      }
    }
  };
  // 插入数据
  static insertData = async (collectionName: string, data: ContextNode[]) => {
    const table = await LanceDB.getCodeTable(collectionName);
    const isCode = !collectionName.endsWith('_non_code');

    // Handle single object
    if (!Array.isArray(data)) {
      data = [data];
    }

    // 获取依赖信息
    const dataWithIds = await Promise.all(
      data.map(async (item: any) => {
        // const nodeGraphTable = await NodeGraphTable.getNodeGraphTable(collectionName);
        // // 查询该nodeid 的所有子依赖
        // const calleeReferences = await nodeGraphTable.findByNodeId(item.node_id, NodeGraphType.dependency);
        // // 查询所有依赖该 nodeid 的函数
        // const callerReferences = await nodeGraphTable.findBySubNodeId(item.node_id, NodeGraphType.dependency);
        // // 查询该nodeid 的所有父依赖
        // const defParentFunctions = await nodeGraphTable.findBySubNodeId(item.node_id, NodeGraphType.parentDependency);
        // // 查询所有被该 nodeid 依赖的函数
        // const defChildFunctions = await nodeGraphTable.findByNodeId(item.node_id, NodeGraphType.parentDependency);

        return {
          id: item.id,
          node_id: item.node_id,
          sub_id: item.sub_id || 0,
          is_sub_node: item.is_sub_node || false,
          file_name: item.file_name,
          file_path: item.file_path,
          code_type: item.code_type,
          name: item.name,
          signature: item.signature,
          line: item.line,
          line_from: item.line_from,
          line_to: item.line_to,
          code_vector: item.code_vector || new Array(isCode ? code_dense_dim : nl_dense_dim).fill(0),
          nl_vector: item.nl_vector || new Array(nl_dense_dim).fill(0),
          sparse_vector_indices: Object.keys(item.sparse_vector || {}).map((k) => BigInt(k)),
          sparse_vector_values: Object.values(item.sparse_vector || {}).map((v) => (v as number[])[0]),
          snippet: item.snippet,
          language: item.language,
          caller_references: [] as string[],
          callee_references: [] as string[],
          def_parent_functions: [] as string[],
          def_child_functions: [] as string[]
        };
      })
    );
    await table.add(dataWithIds);
  };

  // 获取查询结果
  static getTableData = async (data: VectorQuery | Query) => {
    const result = [];
    for await (const batch of data) {
      if (batch) {
        result.push(...batch);
      }
    }

    return result;
  };
  static createIndex = async (collectionName: string) => {
    const table = await LanceDB.getCodeTable(collectionName);
    await LanceDB.createDbIndex(table);

    //
    const cacheTable = await LanceDB.getCacheTable(collectionName);
    await LanceDB.createDbIndex(cacheTable);

    this.logger.info(`为表 ${collectionName} 创建索引完成`);
  };

  // 清除索引库
  static clearLanceDb = async (collectionName: string) => {
    try {
      const lanceDb = await LanceDB.get();
      const nonCodeCollectionName = getNonCodeCollectionName(collectionName);
      const tables = [
        collectionName,
        nonCodeCollectionName,
        getCacheCollectionName(collectionName),
        getCacheCollectionName(nonCodeCollectionName)
      ];
      for (const table of tables) {
        const tableExists = (await lanceDb.tableNames()).includes(table);
        if (tableExists) {
          await lanceDb.dropTable(table);
          this.logger.info(`drop table ${table} done`);
        }
      }
      return true;
    } catch (error: any) {
      this.logger.error('clear lance db failed, 清除lance db失败', error);
      return false;
    }
  };

  // Search by sparse vector
  static searchBySparseVector = async (
    table: Table,
    sparseVector: Record<string, number>,
    outputFields: string[],
    limit: number = 10,
    filterStr: string = ''
  ) => {
    // Convert sparse vector object to arrays
    const indices = Object.keys(sparseVector).map((k) => BigInt(k));
    const values = Object.values(sparseVector).map((v) => v);

    // Build query to find similar sparse vectors
    let query = table.query();

    // Add conditions to find vectors that share indices
    // We'll look for vectors that share at least one index position
    const indexConditions = indices.map((idx) => {
      return `array_contains(sparse_vector_indices, ${idx})`;
    });

    if (indexConditions.length > 0) {
      query = query.where(indexConditions.join(' OR '));
    }
    if (filterStr) {
      query = query.where(filterStr);
    }
    query = query.limit(limit);

    // Get results and then we'll post-process to find the most similar vectors
    const results = await LanceDB.getTableData(query.select([...outputFields]));

    // Calculate similarity scores and sort results
    // const scoredResults = results.map((result) => {
    //   let matchCount = 0;
    //   for (let i = 0; i < indices.length; i++) {
    //     const idx = indices[i];
    //     const val = values[i];
    //     const resultIdxPos = result.sparse_vector_indices.indexOf(idx);
    //     if (resultIdxPos !== -1 && Math.abs(result.sparse_vector_values[resultIdxPos] - val) < 0.1) {
    //       matchCount++;
    //     }
    //     delete result.sparse_vector_indices;
    //     delete result.sparse_vector_values;
    //   }
    //   return {
    //     ...result,
    //     _score: matchCount / indices.length,
    //   };
    // });
    return results;
    // Sort by score descending and take top k results
    // return results.sort((a, b) => b._score - a._score).slice(0, limit);
  };

  // 按文件路径删除数据
  static deleteByCondition = async (collectionName: string, condition: string) => {
    const table = await LanceDB.getTable(collectionName);
    await table.delete(condition);
    await table.close();
  };
}
