CREATE TABLE IF NOT EXISTS repo_status (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  git_url TEXT NOT NULL,
  dir_path TEXT NOT NULL UNIQUE,
  branch TEXT,
  commit_id TEXT NOT NULL,
  updated_at INTEGER NOT NULL,
  created_at INTEGER NOT NULL,
  ide_version TEXT NOT NULL,
  plugin_version TEXT NOT NULL,
  platform TEXT NOT NULL,
  total_files INTEGER DEFAULT 0,
  done_files INTEGER DEFAULT 0,
  is_building BOOLEAN DEFAULT FALSE
); 