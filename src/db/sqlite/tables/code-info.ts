import { Logger } from '@/util/log.js';
import { SqliteDb } from '../index.js';
import { NodeGraphTable } from './node-graph.js';
import { AGENT_NAMESPACE } from '@/util/const.js';
import { GlobalConfig } from '@/util/global.js';

const logger = new Logger('CodeInfoTable');

export interface CodeInfo {
  /** 自增主键 */
  id?: number;
  /** 节点ID */
  node_id: string;
  /** 代码片段 */
  snippet: string;
  /** 文件路径 */
  filepath: string;
  /** 代码类型 */
  code_type: string;
  /** 名称 */
  name: string;
  /** 签名 */
  signature: string;
  /** 行号 */
  line: number;
  /** 起始行 */
  line_from: number;
  /** 结束行 */
  line_to: number;
  /** 编程语言 */
  language: string;
  // 下边的几个字段要从graph中获取，不存储在数据库中
  /** 调用者引用 */
  caller_id_references?: string;
  /** 被调用者引用 */
  callee_id_references?: string;
  /** 父函数定义 */
  def_parent_functions?: string;
  /** 子函数定义 */
  def_child_functions?: string;
}

type CodeInfoCreate = Omit<
  CodeInfo,
  'id' | 'caller_id_references' | 'callee_id_references' | 'def_parent_functions' | 'def_child_functions'
>;

export class CodeInfoTable {
  static table: Map<string, CodeInfoTable> = new Map();

  private static readonly TABLE_FIELDS = [
    'node_id',
    'snippet',
    'filepath',
    'code_type',
    'name',
    'signature',
    'line',
    'line_from',
    'line_to',
    'language'
  ] as const;

  dbName = '';
  collectionName = '';

  constructor(collectionName: string) {
    this.collectionName = collectionName;
    this.dbName = `${collectionName}_code_info`;
  }

  static async init(collectionName: string): Promise<CodeInfoTable> {
    try {
      const instance = new CodeInfoTable(collectionName);
      await instance.createTable();
      return instance;
    } catch (error: any) {
      logger.error(`Failed to initialize CodeInfoTable for collection: ${collectionName}`, error);
      const errorDetails = {
        message: typeof error === 'object' ? error.message : String(error),
        code: error?.code,
        errno: error?.errno,
        sql: error?.sql,
        collectionName
      };
      logger.error('Detailed error information:', errorDetails);
      logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'initCodeInfoTableError',
        millis: 1,
        extra3: GlobalConfig.getConfig().getUsername(),
        extra4: GlobalConfig.getConfig().getRepoPath(),
        extra5: typeof error === 'object' ? error.message : String(error),
        extra6: JSON.stringify(errorDetails)
      });
      throw error;
    }
  }

  async createTable() {
    const db = await SqliteDb.get();
    if (!this.dbName) {
      logger.error('dbName is not set');
      throw new Error('dbName is not set');
    }

    try {
      logger.info(`Creating table ${this.dbName} start`);
      await db.exec(`CREATE TABLE IF NOT EXISTS ${this.dbName} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ${CodeInfoTable.TABLE_FIELDS.map(
          (field) => `${field} ${field.includes('line') ? 'INTEGER' : 'TEXT'} NOT NULL`
        ).join(',\n        ')}
      );
      
      -- Create indexes for specified fields
      CREATE INDEX IF NOT EXISTS idx_${this.dbName}_node_id ON ${this.dbName}(node_id);
      CREATE INDEX IF NOT EXISTS idx_${this.dbName}_filepath ON ${this.dbName}(filepath);
      CREATE INDEX IF NOT EXISTS idx_${this.dbName}_name ON ${this.dbName}(name);
      `);
      logger.info(`Creating table ${this.dbName} done`);
    } catch (error) {
      logger.error(`Failed to create table ${this.dbName}:`, error);
      throw error;
    }
  }

  async create(data: CodeInfoCreate): Promise<number> {
    try {
      if (!data) {
        return 0;
      }
      const db = await SqliteDb.get();
      const result = await db.run(
        `INSERT INTO ${this.dbName} (${CodeInfoTable.TABLE_FIELDS.join(', ')}) 
         VALUES (${CodeInfoTable.TABLE_FIELDS.map(() => '?').join(', ')})`,
        CodeInfoTable.TABLE_FIELDS.map((field) => data[field])
      );
      if (result.lastID === undefined) {
        throw new Error('Failed to get lastID from insert');
      }
      return result.lastID;
    } catch (error: any) {
      logger.error(`Failed to create code info record`, error);
      const errorDetails = {
        message: typeof error === 'object' ? error.message : String(error),
        code: error?.code,
        errno: error?.errno,
        sql: error?.sql,
        data,
        dbName: this.dbName
      };
      logger.error('Detailed error information:', errorDetails);
      logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'createCodeInfoError',
        millis: 1,
        extra3: GlobalConfig.getConfig().getUsername(),
        extra4: GlobalConfig.getConfig().getRepoPath(),
        extra5: typeof error === 'object' ? error.message : String(error),
        extra6: JSON.stringify(errorDetails)
      });
      throw error;
    }
  }

  async update(id: number, data: Partial<CodeInfoCreate>): Promise<void> {
    try {
      const db = await SqliteDb.get();
      const updates = Object.entries(data)
        .map(([key]) => `${key} = ?`)
        .join(', ');
      const values = Object.values(data);

      await db.run(`UPDATE ${this.dbName} SET ${updates} WHERE id = ?`, [...values, id]);
    } catch (error: any) {
      logger.error(`Failed to update code info record with ID: ${id}`, error);
      const errorDetails = {
        message: typeof error === 'object' ? error.message : String(error),
        code: error?.code,
        errno: error?.errno,
        sql: error?.sql,
        id,
        data,
        dbName: this.dbName
      };
      logger.error('Detailed error information:', errorDetails);
      logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'updateCodeInfoError',
        millis: 1,
        extra3: GlobalConfig.getConfig().getUsername(),
        extra4: GlobalConfig.getConfig().getRepoPath(),
        extra5: typeof error === 'object' ? error.message : String(error),
        extra6: JSON.stringify(errorDetails)
      });
      throw error;
    }
  }

  async delete(id: number): Promise<void> {
    try {
      const db = await SqliteDb.get();
      await db.run(`DELETE FROM ${this.dbName} WHERE id = ?`, [id]);
    } catch (error: any) {
      logger.error(`Failed to delete code info record with ID: ${id}`, error);
      const errorDetails = {
        message: typeof error === 'object' ? error.message : String(error),
        code: error?.code,
        errno: error?.errno,
        sql: error?.sql,
        id,
        dbName: this.dbName
      };
      logger.error('Detailed error information:', errorDetails);
      logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'deleteCodeInfoError',
        millis: 1,
        extra3: GlobalConfig.getConfig().getUsername(),
        extra4: GlobalConfig.getConfig().getRepoPath(),
        extra5: typeof error === 'object' ? error.message : String(error),
        extra6: JSON.stringify(errorDetails)
      });
      throw error;
    }
  }

  private processQueryResult(result: CodeInfo | undefined): CodeInfo | undefined {
    if (!result) return undefined;

    // Convert JSON array strings to proper format
    // Handle empty array case which returns '[null]' from SQLite
    const referenceFields = [
      'caller_id_references',
      'callee_id_references',
      'def_parent_functions',
      'def_child_functions'
    ] as const;

    referenceFields.forEach((field) => {
      const value = result[field];
      if (value) {
        const parsed = JSON.parse(value);
        result[field] = parsed[0] === null ? '[]' : JSON.stringify(parsed);
      } else {
        result[field] = '[]';
      }
    });

    return result;
  }

  private processQueryResults(results: CodeInfo[]): CodeInfo[] {
    return results.map((result) => this.processQueryResult(result)!);
  }

  async findById(id: number): Promise<CodeInfo | undefined> {
    try {
      const db = await SqliteDb.get();
      const result = await db.get<CodeInfo>(
        `
        SELECT * FROM ${this.dbName}
        WHERE id = ?
      `,
        [id]
      );
      return this.processQueryResult(result);
    } catch (error: any) {
      logger.error(`Failed to find code info by ID: ${id}`, error);
      const errorDetails = {
        message: typeof error === 'object' ? error.message : String(error),
        code: error?.code,
        errno: error?.errno,
        sql: error?.sql,
        id,
        dbName: this.dbName
      };
      logger.error('Detailed error information:', errorDetails);
      logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'findCodeInfoByIdError',
        millis: 1,
        extra3: GlobalConfig.getConfig().getUsername(),
        extra4: GlobalConfig.getConfig().getRepoPath(),
        extra5: typeof error === 'object' ? error.message : String(error),
        extra6: JSON.stringify(errorDetails)
      });
      throw error;
    }
  }

  async findByNodeId(nodeId: string): Promise<CodeInfo | undefined> {
    try {
      const db = await SqliteDb.get();
      const result = await db.get<CodeInfo>(
        `
        SELECT * FROM ${this.dbName}
        WHERE node_id = ?
      `,
        [nodeId]
      );
      return this.processQueryResult(result);
    } catch (error: any) {
      logger.error(`Failed to find code info by node ID: ${nodeId}`, error);
      const errorDetails = {
        message: typeof error === 'object' ? error.message : String(error),
        code: error?.code,
        errno: error?.errno,
        sql: error?.sql,
        nodeId,
        dbName: this.dbName
      };
      logger.error('Detailed error information:', errorDetails);
      logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'findCodeInfoByNodeIdError',
        millis: 1,
        extra3: GlobalConfig.getConfig().getUsername(),
        extra4: GlobalConfig.getConfig().getRepoPath(),
        extra5: typeof error === 'object' ? error.message : String(error),
        extra6: JSON.stringify(errorDetails)
      });
      throw error;
    }
  }

  async findByFilePath(filepath: string): Promise<CodeInfo[]> {
    try {
      const db = await SqliteDb.get();
      const results = await db.all<CodeInfo[]>(
        `
        SELECT * FROM ${this.dbName}
        WHERE filepath = ?
      `,
        [filepath]
      );
      return this.processQueryResults(results);
    } catch (error: any) {
      logger.error(`Failed to find code info by filepath: ${filepath}`, error);
      const errorDetails = {
        message: typeof error === 'object' ? error.message : String(error),
        code: error?.code,
        errno: error?.errno,
        sql: error?.sql,
        filepath,
        dbName: this.dbName
      };
      logger.error('Detailed error information:', errorDetails);
      logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'findCodeInfoByFilePathError',
        millis: 1,
        extra3: GlobalConfig.getConfig().getUsername(),
        extra4: GlobalConfig.getConfig().getRepoPath(),
        extra5: typeof error === 'object' ? error.message : String(error),
        extra6: JSON.stringify(errorDetails)
      });
      throw error;
    }
  }

  async findByName(name: string): Promise<CodeInfo[]> {
    try {
      const db = await SqliteDb.get();
      const results = await db.all<CodeInfo[]>(
        `
        SELECT * FROM ${this.dbName}
        WHERE name = ?
      `,
        [name]
      );
      return this.processQueryResults(results);
    } catch (error: any) {
      logger.error(`Failed to find code info by name: ${name}`, error);
      const errorDetails = {
        message: typeof error === 'object' ? error.message : String(error),
        code: error?.code,
        errno: error?.errno,
        sql: error?.sql,
        name,
        dbName: this.dbName
      };
      logger.error('Detailed error information:', errorDetails);
      logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'findCodeInfoByNameError',
        millis: 1,
        extra3: GlobalConfig.getConfig().getUsername(),
        extra4: GlobalConfig.getConfig().getRepoPath(),
        extra5: typeof error === 'object' ? error.message : String(error),
        extra6: JSON.stringify(errorDetails)
      });
      throw error;
    }
  }

  async batchCreate(dataList: CodeInfoCreate[]): Promise<number> {
    try {
      if (!dataList || !Array.isArray(dataList) || dataList.length === 0) {
        return 0;
      }
      const db = await SqliteDb.get();

      // SQLite has a limit of 999 variables per statement
      // Each record uses TABLE_FIELDS.length variables
      // Calculate max records per batch to stay under the limit
      const variablesPerRecord = CodeInfoTable.TABLE_FIELDS.length;
      const maxRecordsPerBatch = Math.floor(999 / variablesPerRecord);

      let lastId = 0;

      // Process in batches to avoid exceeding SQLite's variable limit
      for (let i = 0; i < dataList.length; i += maxRecordsPerBatch) {
        const batchData = dataList.slice(i, i + maxRecordsPerBatch);
        const placeholders = `(${CodeInfoTable.TABLE_FIELDS.map(() => '?').join(', ')})`;
        const values = batchData.map((data) => CodeInfoTable.TABLE_FIELDS.map((field) => data[field])).flat();

        const result = await db.run(
          `INSERT INTO ${this.dbName} (${CodeInfoTable.TABLE_FIELDS.join(', ')}) 
           VALUES ${new Array(batchData.length).fill(placeholders).join(',')}`,
          values
        );

        if (result.lastID !== undefined) {
          lastId = result.lastID;
        }
      }

      if (lastId === 0) {
        throw new Error('Failed to get lastID from batch insert');
      }
      return lastId;
    } catch (error: any) {
      logger.error(`Failed to batch create code info records`, error);
      const errorDetails = {
        message: typeof error === 'object' ? error.message : String(error),
        code: error?.code,
        errno: error?.errno,
        sql: error?.sql,
        recordCount: dataList?.length,
        dbName: this.dbName
      };
      logger.error('Detailed error information:', errorDetails);
      logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'batchCreateCodeInfoError',
        millis: 1,
        extra3: GlobalConfig.getConfig().getUsername(),
        extra4: GlobalConfig.getConfig().getRepoPath(),
        extra5: typeof error === 'object' ? error.message : String(error),
        extra6: JSON.stringify(errorDetails)
      });
      throw error;
    }
  }

  async dropTable() {
    try {
      const db = await SqliteDb.get();
      await db.exec(`DROP TABLE IF EXISTS ${this.dbName}`);
      CodeInfoTable.table.clear();
      return true;
    } catch (error: any) {
      logger.error(`Failed to drop table ${this.dbName}`, error);
      const errorDetails = {
        message: typeof error === 'object' ? error.message : String(error),
        code: error?.code,
        errno: error?.errno,
        sql: error?.sql,
        dbName: this.dbName
      };
      logger.error('Detailed error information:', errorDetails);
      logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'dropCodeInfoTableError',
        millis: 1,
        extra3: GlobalConfig.getConfig().getUsername(),
        extra4: GlobalConfig.getConfig().getRepoPath(),
        extra5: typeof error === 'object' ? error.message : String(error),
        extra6: JSON.stringify(errorDetails)
      });
      throw error;
    }
  }

  static async getCodeInfoTable(collectionName: string): Promise<CodeInfoTable> {
    try {
      if (!this.table.has(collectionName)) {
        const instance = await CodeInfoTable.init(collectionName);
        this.table.set(collectionName, instance);
      }
      return this.table.get(collectionName)!;
    } catch (error: any) {
      logger.error(`Failed to get code info table for collection: ${collectionName}`, error);
      const errorDetails = {
        message: typeof error === 'object' ? error.message : String(error),
        code: error?.code,
        errno: error?.errno,
        sql: error?.sql,
        collectionName
      };
      logger.error('Detailed error information:', errorDetails);
      logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'getCodeInfoTableError',
        millis: 1,
        extra3: GlobalConfig.getConfig().getUsername(),
        extra4: GlobalConfig.getConfig().getRepoPath(),
        extra5: typeof error === 'object' ? error.message : String(error),
        extra6: JSON.stringify(errorDetails)
      });
      throw error;
    }
  }

  async deleteByFilePath(filepath: string): Promise<void> {
    try {
      const db = await SqliteDb.get();
      await db.run(`DELETE FROM ${this.dbName} WHERE filepath = ?`, [filepath]);
    } catch (error: any) {
      logger.error(`Failed to delete code info by filepath: ${filepath}`, error);
      throw error;
    }
  }
}
