import { SqliteDb } from '../index.js';

export interface FileStatus {
  /** 自增主键 */
  id?: number;
  /** 代码仓库地址 */
  git_url: string;
  /** 代码仓库路径(相对系统根目录的绝对路径) */
  dir_path: string;
  /** 当前分支 */
  branch: string;
  /** 当前index的最后一次commitid */
  commit_id: string;
  /** 文件名称 */
  filename: string;
  /** 文件路径(相对仓库根目录) */
  filepath: string;
  /** 文件哈希值 */
  hash: string;
  /** 当前文件状态: indexing-索引中, indexed-已索引, deleted-已删除 */
  status: 'indexing' | 'indexed' | 'deleted';
  /** 最后更新时间戳 */
  updated_at: number;
  /** 创建时间戳 */
  created_at: number;
}

export class FileStatusTable {
  static async create(data: Omit<FileStatus, 'id'>) {
    const db = await SqliteDb.get();
    const result = await db.run(
      `INSERT INTO file_status (
        git_url, dir_path, filename, filepath,
        hash, status, updated_at, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        data.git_url,
        data.dir_path,
        data.filename,
        data.filepath,
        data.hash,
        data.status,
        data.updated_at,
        data.created_at
      ]
    );
    return result.lastID;
  }

  static async update(filepath: string, data: Partial<Omit<FileStatus, 'id'>>) {
    const db = await SqliteDb.get();

    // 首先检查记录是否存在
    const existing = await this.findByFilePath(filepath);

    if (!existing) {
      return await this.create({
        git_url: data.git_url ?? '',
        dir_path: data.dir_path ?? '',
        hash: data.hash ?? '',
        filepath: filepath,
        filename: filepath.split('/').pop() ?? '',
        status: data.status ?? 'indexing',
        updated_at: data.updated_at ?? Date.now(),
        created_at: data.created_at ?? Date.now()
      } as Omit<FileStatus, 'id'>);
    }

    // 如果记录存在，执行更新
    const updates = Object.entries(data)
      .map(([key]) => `${key} = ?`)
      .join(', ');
    const values = Object.values(data);

    await db.run(`UPDATE file_status SET ${updates} WHERE filepath = ?`, [...values, filepath]);
  }

  static async deleteByDirPath(dirPath: string) {
    const db = await SqliteDb.get();
    await db.run('DELETE FROM file_status WHERE dir_path = ?', [dirPath]);
  }

  static async findByHash(hash: string) {
    const db = await SqliteDb.get();
    return await db.get<FileStatus>('SELECT * FROM file_status WHERE hash = ?', [hash]);
  }

  static async findByFilePath(filepath: string) {
    const db = await SqliteDb.get();
    return await db.get<FileStatus>('SELECT * FROM file_status WHERE filepath = ?', [filepath]);
  }
  // 判断文件是否被索引过
  static async checkFileStatus(filepath: string): Promise<boolean> {
    const cacheInfo = await this.findByFilePath(filepath);
    if (!cacheInfo) {
      return false;
    }
    return true;
  }

  static async findByDirPath(dirPath: string) {
    const db = await SqliteDb.get();
    return await db.all<FileStatus[]>('SELECT * FROM file_status WHERE dir_path = ?', [dirPath]);
  }

  static async findByDirPathPaged(dirPath: string, limit: number, offset: number) {
    const db = await SqliteDb.get();
    return await db.all<FileStatus[]>('SELECT * FROM file_status WHERE dir_path = ? LIMIT ? OFFSET ?', [
      dirPath,
      limit,
      offset
    ]);
  }

  static async countByDirPath(dirPath: string) {
    const db = await SqliteDb.get();
    const result = await db.get<{ count: number }>('SELECT COUNT(*) as count FROM file_status WHERE dir_path = ?', [
      dirPath
    ]);
    return result?.count || 0;
  }
  static async findAll() {
    const db = await SqliteDb.get();
    return await db.all<FileStatus[]>('SELECT * FROM file_status');
  }
}

// 添加辅助函数来验证必需字段
function isValidFileStatus(data: Partial<Omit<FileStatus, 'id'>>): boolean {
  const requiredFields = ['git_url', 'dir_path', 'hash'];

  return requiredFields.every((field) => field in data);
}
