import { SqliteDb } from '../index.js';

export interface UserStatus {
  /** 自增主键 */
  id?: number;
  /** 索引构建状态: 0-暂停, 1-运行中, 2-完成 */
  status: 0 | 1 | 2;
  /** 代码仓库路径(相对系统根目录的绝对路径) */
  dir_path: string;
  /** 总文件数量 */
  total_file_num: number;
  /** 成功处理文件数量 */
  success_file_num: number;
  /** 失败处理文件数量 */
  failed_file_num: number;
  /** 额外信息(JSON字符串) */
  extra?: string;
}

export class UserStatusTable {
  static async create(data: Omit<UserStatus, 'id'>) {
    const db = await SqliteDb.get();
    const result = await db.run(
      `INSERT INTO user_status (
        status, dir_path, total_file_num, success_file_num,
        failed_file_num, extra
      ) VALUES (?, ?, ?, ?, ?, ?)`,
      [
        data.status,
        data.dir_path,
        data.total_file_num,
        data.success_file_num,
        data.failed_file_num,
        data.extra,
      ]
    );
    return result.lastID;
  }

  static async update(dirPath: string, data: Partial<Omit<UserStatus, 'id'>>) {
    // 如果dirPath不存在，则创建，如果存在则更新
    const db = await SqliteDb.get();
    const userStatus = await db.get<UserStatus>(`SELECT * FROM user_status WHERE dir_path = ?`, [dirPath]);
    if (!userStatus) {
        await db.run(`INSERT INTO user_status (dir_path, status, total_file_num, success_file_num, failed_file_num, extra) VALUES (?, ?, ?, ?, ?, ?)`, [dirPath, data.status ?? 1, data.total_file_num ?? 0, data.success_file_num ?? 0, data.failed_file_num ?? 0, data.extra ?? '']);
    } else {
      const updates = Object.entries(data)
      .map(([key]) => `${key} = ?`)
      .join(', ');
    const values = Object.values(data);
    
    await db.run(
      `UPDATE user_status SET ${updates} WHERE dir_path = ?`,
      [...values, dirPath]
    );
    }
  }

  static async delete(dirPath: string) {
    const db = await SqliteDb.get();
    await db.run('DELETE FROM user_status WHERE dir_path = ?', [dirPath]);
  }


  static async findByDirPath(dirPath: string) {
    const db = await SqliteDb.get();
    return await db.get<UserStatus>('SELECT * FROM user_status WHERE dir_path = ?', [dirPath]);
  }
} 