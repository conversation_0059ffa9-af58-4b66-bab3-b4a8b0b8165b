import * as path from 'path';
import { readFile } from 'node:fs/promises';
import { getParserForFile, getQueryForFile } from '@/tree-sitter/TreeSitter';
import { ContextNode } from '../node/ContextNode';
import { VariableNode } from '../node/VariableNode';
import { Logger } from '@/util/log';
import { AGENT_NAMESPACE } from '@/util/const';
import { GlobalConfig } from '@/util/global';
const logger = new Logger('code');
import {
  ANONYMOUS_INNER_CLASS,
  getFunctionCodeSnippet,
  getFunctionComments,
  getFunctionName,
  getFunctionSignature,
  getModuleName,
  getStructName,
  getVariableCodeSnippet,
  getVariableName,
} from '../analyze/program_utils';
import { QueryCapture, SyntaxNode } from 'web-tree-sitter';
// Updated mapping of file extensions to parsers
export const PARSERS = {
  '.py': 'python',
  '.js': 'javascript',
  '.mjs': 'javascript', // mjs file extension stands for "module JavaScript."
  '.go': 'go',
  '.bash': 'bash',
  '.c': 'c',
  '.cc': 'cpp',
  '.cs': 'c_sharp',
  '.cl': 'commonlisp',
  '.cpp': 'cpp',
  '.css': 'css',
  '.dockerfile': 'dockerfile',
  '.dot': 'dot',
  '.el': 'elisp',
  '.ex': 'elixir',
  '.elm': 'elm',
  '.et': 'embedded_template',
  '.erl': 'erlang',
  '.gomod': 'gomod',
  '.hack': 'hack',
  '.hs': 'haskell',
  '.hcl': 'hcl',
  '.html': 'html',
  '.java': 'java',
  '.jsdoc': 'jsdoc',
  '.json': 'json',
  '.jl': 'julia',
  '.kt': 'kotlin',
  '.lua': 'lua',
  '.mk': 'make',
  // ".md": "markdown", // https://github.com/ikatyang/tree-sitter-markdown/issues/59
  '.m': 'objc',
  '.ml': 'ocaml',
  '.pl': 'perl',
  '.php': 'php',
  '.ql': 'ql',
  '.r': 'r',
  '.R': 'r',
  '.regex': 'regex',
  '.rst': 'rst',
  '.rb': 'ruby',
  '.rs': 'rust',
  '.scala': 'scala',
  '.sql': 'sql',
  // '.sqlite': 'sqlite',
  '.toml': 'toml',
  '.tsq': 'tsq',
  '.tsx': 'tsx',
  '.ts': 'typescript',
  '.yaml': 'yaml',
  '.vue': 'html',
};

export const filename_to_lang = (filename: string) => {
  const file_extension = path.extname(filename);
  const lang = PARSERS[file_extension as keyof typeof PARSERS];
  return lang;
};

export async function getChunksAtFunctionLevel(filepath: string): Promise<{ captures: QueryCapture[]; lang: string }> {
  const filename = path.basename(filepath);
  const lang = filename_to_lang(filename);
  if (!lang) {
    console.error(`Unsupported file type: ${filename}`);
    return { captures: [], lang: '' };
  }
  const content = await readFile(filepath, 'utf-8');
  const captures = await queryChunksAtFunctionLevel(content, lang);
  return { captures, lang };
}

export const getChunksWithGivenText = async (code_snippet: string, lang: string): Promise<QueryCapture[]> => {
  return queryChunksAtFunctionLevel(code_snippet, lang);
};

export const queryChunksAtFunctionLevel = async (content: string, lang: string): Promise<QueryCapture[]> => {
  const parser = await getParserForFile(lang);
  if (!parser) {
    return [];
  }

  const tree = parser.parse(content);
  const query = await getQueryForFile(lang);
  if (!query) {
    logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'unknownError',
      millis: 1,
      extra3: GlobalConfig.getConfig().getUsername(),
      extra4: GlobalConfig.getConfig().getRepoPath(),
      extra5: `No query found for language ${lang}`,
    });
    console.error(`No query found for language ${lang}`);
    return [];
  }
  const captures = query?.captures(tree.rootNode);
  return captures;
};

export const initFunctionContextNode = async (defNode: SyntaxNode, filePath: string, lang: string, tag: string) => {
  // Prepare Fields for ContextNode
  const nameNode = await getFunctionName(defNode, lang);
  const name = nameNode ? nameNode.text : '';

  let signature = '';
  let codeSnippet = null;
  // todo: 这个地方和python 的不一样，应该怎么获取
  const line = defNode.startPosition?.row;
  const lineTo = defNode.endPosition?.row;

  signature = await getFunctionSignature(defNode, nameNode, lang);
  codeSnippet = await getFunctionCodeSnippet(defNode, lang);

  if (!codeSnippet) {
    // It is an interface
    return null;
  }

  const [lineFrom, docstring] = await getFunctionComments(defNode, lang);

  // construct module 这里是干啥用的
  const moduleName = getModuleName(defNode, lang);
  const [structNode, structName] = getStructName(defNode, lang);
  let anonymousParentName = '';

  if (structNode && structName === ANONYMOUS_INNER_CLASS) {
    for (const child of structNode.children) {
      if (child.type === 'simple_identifier') {
        anonymousParentName = child.text;
        break;
      }
    }
  }

  // Create a ContextNode
  const contextNode = new ContextNode(filePath, lang);
  contextNode.setName(name);
  contextNode.setSignature(signature);
  contextNode.setCodeSnippet(codeSnippet);
  contextNode.setRange(line, lineFrom, lineTo);
  contextNode.setDocstring(docstring);
  contextNode.setModule(moduleName);
  contextNode.setStructName(structName);
  contextNode.setAnonymousParentFunctionName(anonymousParentName);

  return contextNode;
};

export const initVariableContextNode = async (defNode: SyntaxNode, filePath: string, lang: string, tag: string) => {
  const nameNode = await getVariableName(defNode, lang);
  const name = nameNode ? nameNode.text : '';

  const signature = '';
  const line = defNode.startPosition?.row;
  const lineFrom = line;
  const lineTo = defNode.endPosition?.row;
  const codeSnippet = await getVariableCodeSnippet(defNode, lang);

  if (!codeSnippet) {
    // It is an interface
    return null;
  }

  // construct docstring, module and struct name
  const docstring = '';
  const moduleName = '';
  const structName = '';

  // Init VariableNode
  const contextNode = new VariableNode(filePath, lang);
  contextNode.setName(name);
  contextNode.setSignature(signature);
  contextNode.setCodeSnippet(codeSnippet);
  contextNode.setRange(line, lineFrom, lineTo);
  contextNode.setDocstring(docstring);
  contextNode.setModule(moduleName);
  contextNode.setStructName(structName);

  return contextNode;
};
