import { nanoid } from 'nanoid';
import { generateOnelineSummary } from '../analyze/util';
import { CodeInfo } from '@/db/sqlite/tables/code-info';
// lancedb 数据库自带的模糊搜索，全文检索，关键词检索。使用 rerank_query,取重合度最高的 40 个结果。
//
export class ContextNode {
  id: string; // 自增主键
  node_id: string; // 一个函数分片之后使用同一个node_id,不同的 subid 来区分是哪一块。函数 id 是唯一。文件删除的话 node_id 会断层。
  sub_id: number; // 默认是 0
  name: string; // 函数名或者变量名。
  signature: string; //函数签名
  code_type: string; // function variable
  line: number; // 函数第一行所在的行号
  line_from: number; // 注释所在的行号，没有注释的话=line
  line_to: number; // 函数最后一行所在的行号
  docstring: string; // 函数注释
  module: string; // 如果没有就是空，java 里边是 package name
  struct_name: string; // class 名称
  snippet: string; // 代码片段，整个函数体
  anonymous_parent_function_name: string; // 匿名函数的父节点名字
  language: string; // 代码使用的语言
  is_sub_node: boolean; // 判断是不是被切片，召回的时候要返回完整的片段。
  original_snippet: string; // 这个没有存进数据库，完整的代码片段。切分时会置为空字符串
  base_offset: number; // 针对 html 中内嵌 JavaScript 的情况，获取是 从第几行开始，不会存数据库。生成 line 的时候会加上这个偏移量。
  file_path: string; // 文件路径，完整的，包含文件名
  file_name: string; // 文件名
  generated_docstring: string | undefined; // 把 docstring，code_type, name，signature,module, filename 拼接起来，生成一个摘要。生成 nl_vector。

  context: Record<string, any> | null; // toDict 的结果，上边的这些字段整合一下。
  callee_name_references: Set<string>; // 调用的其他人的函数名
  // callee_id_references: string[]; // 调用的其他人多的函数 id，是nodeid。// 可能要重新设计，目前可能没有用上。
  // caller_references: string[];
  // callee_references: string[];
  // caller_id_references: string[];
  // def_parent_functions: string[]; // 匿名函数父节点 id。
  // def_child_functions: string[]; // 函数本身包含匿名函数，匿名函数的 id。

  constructor(filePath: string, lang: string) {
    this.file_path = filePath.toString();
    this.file_name = filePath.split('/').pop() || '';
    const id = nanoid();
    this.id = id + '';
    this.node_id = id;
    this.sub_id = 0;
    this.name = '';
    this.signature = '';
    this.code_type = 'Function';
    this.line = 0;
    this.line_from = 0;
    this.line_to = 0;
    this.docstring = '';
    this.module = '';
    this.struct_name = '';
    this.snippet = '';
    this.anonymous_parent_function_name = '';
    this.language = lang;
    this.is_sub_node = false;
    this.original_snippet = '';
    this.callee_name_references = new Set();
    this.base_offset = 0;
    this.context = null;
  }

  setRelativePath(repoDir: string): void {
    this.file_path = this.file_path.replace(`${repoDir}/`, '');
  }

  setName(name: string): void {
    this.name = name;
  }

  setSignature(signature: string): void {
    this.signature = signature;
  }

  setCodeSnippet(codeSnippet: string): void {
    this.snippet = codeSnippet;
  }

  setAnonymousParentFunctionName(functionName: string): void {
    this.anonymous_parent_function_name = functionName;
  }

  setRange(line: number, line_from: number, line_to: number): void {
    this.line = line + 1;
    this.line_from = line_from + 1;
    this.line_to = line_to + 1;
  }

  setDocstring(docstring: string): void {
    this.docstring = docstring;
  }

  setModule(module: string): void {
    this.module = module;
  }

  setStructName(structName: string): void {
    this.struct_name = structName;
  }

  setContext(context: Record<string, any>): void {
    this.context = context;
  }

  setBaseOffset(baseOffset: number): void {
    this.base_offset = baseOffset;
  }

  setCodeType(codeType: string): void {
    this.code_type = codeType;
  }

  setSubId(subId: number): void {
    this.sub_id = subId;
    this.is_sub_node = true;
  }

  setOriginalSnippet(snippet: string): void {
    this.original_snippet = snippet;
  }
  setNodeId(nodeId: string): void {
    this.node_id = nodeId;
  }

  clone(): ContextNode {
    const cloned = new ContextNode(this.file_path, this.language);
    Object.assign(cloned, this);
    // Recreate collection properties to avoid sharing references
    cloned.callee_name_references = new Set(this.callee_name_references);
    return cloned;
  }

  toDict(): Record<string, any> {
    if (!this.context) {
      this.context = {
        module: this.module,
        file_path: this.file_path,
        file_name: this.file_name,
        struct_name: this.struct_name,
        snippet: this.snippet,
        original_snippet: this.original_snippet,
        is_sub_node: this.is_sub_node,
        language: this.language,
      };
    }

    const contextDict = {
      node_id: this.id,
      sub_node_id: this.sub_id,
      name: this.name,
      signature: this.signature,
      code_type: this.code_type,
      docstring: this.docstring,
      line: this.line + this.base_offset,
      line_from: this.line_from + this.base_offset,
      line_to: this.line_to + this.base_offset,
      ...this.context,
    };
    const generated_docstring = generateOnelineSummary(this);
    return {
      ...contextDict,
      generated_docstring,
    };
  }

  toCodeInfo(): Omit<CodeInfo, 'id'> {
    return {
      node_id: this.node_id,
      snippet: this.snippet,
      filepath: this.file_path,
      code_type: this.code_type,
      name: this.name,
      signature: this.signature,
      line: this.line,
      line_from: this.line_from,
      line_to: this.line_to,
      language: this.language,
    };
  }

  toString(): string {
    return this.name;
  }
}
