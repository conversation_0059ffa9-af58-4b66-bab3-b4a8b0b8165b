import { ContextNode } from '../node/ContextNode';

export function generateOnelineSummary(chunk: ContextNode): string {
  // Helper function to convert to human readable format (similar to inflection.humanize + underscore)
  const humanize = (str: string): string => {
    return str
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/[_-]/g, ' ') // Replace underscores and hyphens with spaces
      .toLowerCase()
      .trim();
  };

  const name = humanize(chunk.name);
  const signature = humanize(chunk.signature);
  const docstring = chunk.docstring ? `that does ${chunk.docstring} ` : '';
  const splitedFileName = humanize(chunk.file_name);

  let context = `module ${chunk.module} file name: ${chunk.file_name} (${splitedFileName})`;
  if (chunk.struct_name) {
    const structName = humanize(chunk.struct_name);
    context = `defined in struct ${structName} ${context}`;
  }

  const textRepresentation = `${chunk.code_type} ${name} ` + `${docstring}` + `defined as ${signature} ` + `${context}`;

  // Split by non-word characters and filter out empty strings
  return textRepresentation
    .split(/\W+/)
    .filter((token) => token.length > 0)
    .join(' ');
}
