import {
  getChunksAtFunctionLevel,
  getChunksWithGivenText,
  initFunctionContextNode,
  initVariableContextNode,
} from '../chunk/code';
import * as path from 'path';
import { ContextNode } from '../node/ContextNode';
import { AutoTokenizer } from '../chunk/Tokenlizer';
import * as fs from 'fs';
import { promisify } from 'util';
import { TextChunkNode } from '../node/TextChunkNode';
import { constructSymbolTable } from './html_utils';
import { SyntaxNode as Node, QueryCapture, SyntaxNode } from 'web-tree-sitter';
const readFile = promisify(fs.readFile);
import { Logger } from '@/util/log';
const logger = new Logger('code_utils');

// 对于非代码文件，直接将文件内容分割成多个文本块
export async function parseNonCodeFile(filePath: string): Promise<TextChunkNode[]> {
  const textChunkNodes: TextChunkNode[] = [];

  try {
    const text = await readFile(filePath, 'utf-8');
    const maxLen = 1024;
    const overlap = 100;

    for (let i = 0; i < text.length; i += maxLen - overlap) {
      const subText = text.slice(i, i + maxLen);
      const textChunkNode: TextChunkNode = new TextChunkNode(subText, filePath, 'text');

      textChunkNodes.push(textChunkNode);
    }
  } catch (e) {
    logger.warn(`Exception ${e} happens when open file: ${filePath}`);
  }

  return textChunkNodes;
}

export const process_code_files = async (files: string[]): Promise<ContextNode[]> => {
  return parse_files(files, true);
};
export const process_none_code_files = async (files: string[]): Promise<ContextNode[]> => {
  return parse_files(files, false);
};

export const parse_files = async (files: string[], is_code_files: boolean): Promise<ContextNode[]> => {
  const node_list: ContextNode[] = [];
  const total = files.length;

  for await (const [index, file] of files.entries()) {
    if (is_code_files) {
      const list = await parse_code_file(file);
      node_list.push(...list);
    } else {
      // todo: 非代码文件处理
      const list = (await parseNonCodeFile(file)) as unknown as ContextNode[];
      node_list.push(...list);
    }
  }

  logger.info(`Completed processing ${total} files`);
  return node_list;
};
export const PROGRAM_FILES = [
  'java',
  'kotlin',
  'c',
  'cpp',
  'python',
  'javascript',
  'typescript',
  'tsx',
  'rust',
  'go',
  'php',
  'ruby',
];

export const HTML_FILES = ['html'];
export const parse_code_file = async (filepath: string): Promise<ContextNode[]> => {
  const { captures, lang } = await getChunksAtFunctionLevel(filepath);
  const context_node_list: ContextNode[] = [];
  if (PROGRAM_FILES.includes(lang)) {
    context_node_list.push(...(await parse_program_file(filepath, captures, lang)));
  } else if (HTML_FILES.includes(lang)) {
    context_node_list.push(...(await parseHtmlFile(filepath, captures, lang)));
  } else {
    logger.warn(`unsupported file type----${filepath} ${lang}`);
  }

  return context_node_list;
};

export async function parseHtmlFile(file_path: string, captures: QueryCapture[], lang: string): Promise<ContextNode[]> {
  const contextNodeList: ContextNode[] = [];
  const symbolTable = constructSymbolTable(captures);
  const elementMap: Map<Node, ContextNode> = new Map();
  let baseOffset = 0;
  let subCaptures;
  let subLang;
  for (const { node, name } of captures) {
    if (name === 'javascript.code') {
      baseOffset = node.startPosition.row;
      subLang = 'javascript';
      subCaptures = await getChunksWithGivenText(node.text, subLang);
    } else if (name === 'typescript.code') {
      baseOffset = node.startPosition.row;
      subLang = 'typescript';
      subCaptures = await getChunksWithGivenText(node.text, subLang);
    } else {
      continue;
    }

    for (const { node: subNode, name: subTag } of subCaptures as QueryCapture[]) {
      if (subTag === 'definition.method' || subTag === 'definition.function') {
        const contextNode = await initFunctionContextNode(subNode, file_path, subLang, subTag);
        if (!contextNode) {
          continue;
        }
        contextNode.setBaseOffset(baseOffset);
        contextNodeList.push(contextNode);

        if (symbolTable[contextNode.name]) {
          for (const elementNode of symbolTable[contextNode.name]) {
            let elementContextNode: ContextNode | undefined = elementMap.get(elementNode);
            if (!elementContextNode) {
              elementContextNode = initElementContextNode(elementNode, file_path, lang);
              if (!elementContextNode) {
                continue;
              }
              elementMap.set(elementNode, elementContextNode);
            }
            elementContextNode.callee_name_references.add(contextNode.name);
          }
        }
      } else if (subTag === 'name.reference.call') {
        for (let i = contextNodeList.length - 1; i >= 0; i--) {
          const contextNode = contextNodeList[i];
          if (subNode.startPosition.row >= contextNode.line_from && subNode.endPosition.row <= contextNode.line_to) {
            contextNode.callee_name_references.add(subNode.text);
          }
        }
      }
    }
  }

  return contextNodeList;
}

export function initElementContextNode(
  defNode: SyntaxNode,
  filePath: string,
  lang: string,
  tag: string = ''
): ContextNode {
  // Prepare Fields for ContextNode
  // method name
  let name = '';
  for (const child of defNode.children) {
    if (child.type === 'start_tag') {
      for (const subChild of child.children) {
        if (subChild.type === 'tag_name') {
          name = subChild.text;
          break;
        }
      }
      if (name !== '') {
        break;
      }
    }
  }

  const signature = '';
  const codeSnippet = defNode.text;

  const line = defNode.startPosition.row;
  const lineFrom = defNode.startPosition.row;
  const lineTo = defNode.endPosition.row;

  const docstring = '';

  // construct module
  // search parent
  const moduleName = '';
  const structName = '';
  const anonymousParentName = '';

  // Create a ContextNode
  const contextNode = new ContextNode(filePath, lang);
  contextNode.setName(name);
  contextNode.setSignature(signature);
  contextNode.setCodeSnippet(codeSnippet);
  contextNode.setRange(line, lineFrom, lineTo);
  contextNode.setDocstring(docstring);
  contextNode.setModule(moduleName);
  contextNode.setStructName(structName);
  contextNode.setAnonymousParentFunctionName(anonymousParentName);
  contextNode.setCodeType('Element');

  return contextNode;
}

const parse_program_file = async (filepath: string, captures: QueryCapture[], lang: string) => {
  const context_node_list: ContextNode[] = [];
  for (const { node, name } of captures) {
    if (name === 'definition.method' || name === 'definition.function') {
      const context_node = await initFunctionContextNode(node, filepath, lang, name);
      if (!context_node) {
        continue;
      }
      context_node_list.push(context_node);
      // self.add_node_to_context_dict(context_node=context_node)
    } else if (name === 'definition.variable.global') {
      const context_node = await initVariableContextNode(node, filepath, lang, name);
      if (!context_node) {
        continue;
      }
      context_node_list.push(context_node);
    } else if (name === 'name.reference.call') {
      // identify the range
      for (let i = context_node_list.length - 1; i >= 0; i--) {
        const context_node = context_node_list[i];
        if (node.startPosition?.row >= context_node.line_from && node.endPosition?.row <= context_node.line_to) {
          context_node.callee_name_references.add(node.text);
        }
      }
    }
  }
  return context_node_list;
};

export function splitNodeWithLargeSnippet(
  contextNode: ContextNode,
  tokenizer: AutoTokenizer,
  maxSnippetTokens: number = 1024
): ContextNode[] {
  const snippet = contextNode.snippet;
  const snippetTokens = tokenizer.encode(snippet);

  if (snippetTokens.length > maxSnippetTokens) {
    const subContextNodeList: ContextNode[] = [];

    // Split snippet tokens with slide window (step=maxSnippetTokens - 100)
    const overlapTokenLen = 100;
    const subSnippetTokensList: string[][] = [];

    // Create sliding windows of tokens
    for (let i = 0; i < snippetTokens.length; i += maxSnippetTokens - overlapTokenLen) {
      subSnippetTokensList.push(snippetTokens.slice(i, i + maxSnippetTokens));
    }

    const subSnippetList = tokenizer.batchDecode(subSnippetTokensList);

    // Create sub context nodes for each snippet
    subSnippetList.forEach((subSnippet, i) => {
      const subContextNode = structuredClone(contextNode);
      subContextNode.setSubId(i);
      subContextNode.setCodeSnippet(subSnippet);
      subContextNode.setOriginalSnippet(snippet);
      subContextNode.setContext({});
      subContextNodeList.push(subContextNode);
    });

    return subContextNodeList;
  } else {
    return [contextNode];
  }
}
