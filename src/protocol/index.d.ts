import type { IdeCommonMessage } from './messenger';
export interface IdeSettings {
  dirPath: string; // 工作目录
  fileRetryTime: number; // 索引构建失败几次算失败
  modelRetryTime: number; // 调用模型多少次失败算失败
  enableRepoIndex: boolean; // 是否开启仓库索引
  maxIndexSpace: number; // 最大索引空间大小（单位：GB）
  proxyUrl: string; // 代理地址
  agentPreference: string; // 智能体偏好 速度还是效果优先
}
export interface RepoInfo {
  git_url: string;
  dir_path: string;
  commit: string;
  branch: string;
}
export interface IdeInfo {
  version: string;
}

type ChatHistory = {
  role: 'user' | 'assistant';
  content: string;
};

export type SearchSearchParams = {
  query: string;
  chatHistory: ChatHistory[];
  topK?: number;
  targetDirectory?: string[];
};
export enum STATUS {
  OK = 'ok',
  FAILED = 'failed'
}
export type ResponseBase<T> = {
  status: STATUS;
  message?: string;
  common?: IdeCommonMessage;
  data?: T;
  code?: number;
};
