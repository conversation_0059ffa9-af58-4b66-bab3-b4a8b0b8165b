import type { IdePlatform } from './ideCore';
import type { IProtocol } from './index';

export interface IdeCommonMessage {
  pluginVersion: string;
  version: string;
  platform: IdePlatform;
  cwd: string;
  repo: {
    git_url: string;
    dir_path: string;
    commit: string;
  };
}
export interface AgentCommonMessage {
  version: string;
}
export interface Message<T = any> {
  common?: IdeCommonMessage | AgentCommonMessage;
  messageType: string;
  messageId: string;
  data: T;
}

export interface FromMessage<FromProtocol extends IProtocol, T extends keyof FromProtocol> {
  messageType: T;
  messageId: string;
  data: FromProtocol[T][1];
}

export interface IMessenger<ToProtocol extends IProtocol, FromProtocol extends IProtocol> {
  onError(handler: (error: Error) => void): void;
  send<T extends keyof FromProtocol>(messageType: T, data: FromProtocol[T][0], messageId?: string): string;

  on<T extends keyof ToProtocol>(
    messageType: T,
    handler: (message: Message<ToProtocol[T][0]>) => Promise<ToProtocol[T][1]> | ToProtocol[T][1]
  ): void;

  request<T extends keyof FromProtocol>(messageType: T, data: FromProtocol[T][0]): Promise<FromProtocol[T][1]>;

  invoke<T extends keyof ToProtocol>(messageType: T, data: ToProtocol[T][0], messageId?: string): ToProtocol[T][1];

  setIsInited(isInited: boolean): void;
}
