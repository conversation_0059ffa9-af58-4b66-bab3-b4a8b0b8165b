import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { McpConnection, McpTool } from './types';
import { ServerConfigSchema } from './settingSchema';
import { ListToolsResultSchema } from '@modelcontextprotocol/sdk/types.js';
import { formatValidationError } from './parserHelper/validationHelper';
import { MCPLogger } from '@/util/log';
import { getUserNodePath } from './utils';

/** MCP连接请求的默认超时时间（毫秒） */
const DEFAULT_REQUEST_TIMEOUT_MS = 5000;

/**
 * MCP连接管理器
 * 负责管理所有MCP服务器的连接生命周期，包括：
 * - 创建和维护连接
 * - 建立和断开连接
 * - 管理连接状态
 * - 处理连接错误
 */
export class McpConnectionManager {
  /** 当前活动的MCP连接列表 */
  private connections: McpConnection[] = [];

  /** MCP客户端版本号 */
  private clientVersion: string;

  /** 服务器状态变更的回调函数 */
  private onServerChange: () => void;

  private logger;

  /**
   * 创建MCP连接管理器实例
   * @param clientVersion - MCP客户端版本号
   * @param onServerChange - 服务器状态变更时的回调函数
   */
  constructor(clientVersion: string, onServerChange: () => void) {
    this.clientVersion = clientVersion;
    this.onServerChange = onServerChange;
    this.logger = new MCPLogger(`mcp-connection-${this.clientVersion}`);
  }

  /**
   * 获取所有活动的MCP连接
   * @returns 当前所有的MCP连接列表
   */
  public getConnections(): McpConnection[] {
    return this.connections;
  }

  /**
   * 获取指定名称的MCP连接
   * @param name - MCP服务器名称
   * @returns 找到的连接对象，如果不存在则返回undefined
   */
  public getConnection(name: string): McpConnection | undefined {
    return this.connections.find((conn) => conn.server.name === name);
  }

  /**
   * 创建新的MCP连接对象
   * 根据配置创建客户端实例和传输层，但不建立实际连接
   *
   * @param name - MCP服务器名称
   * @param config - 服务器配置信息
   * @returns 新创建的连接对象
   */
  public async createConnection(name: string, jsonConfig: unknown): Promise<McpConnection> {
    this.logger.info('createConnection start', name, jsonConfig);

    // 如果解析失败的，可以字节返回disconnected
    const result = ServerConfigSchema.safeParse(jsonConfig);

    if (!result.success) {
      const connection: McpConnection = {
        server: {
          name,
          config: JSON.stringify(result),
          status: 'disconnected',
          error: formatValidationError(result.error, 'MCP设置文件').message
        }
      };

      this.logger.info(`ServerConfigSchema 解析失败: ${name}`, connection);

      this.connections.push(connection);

      return connection;
    }

    const config = result.data;

    // 创建客户端实例
    const client = new Client(
      {
        name: 'Kwaipilot-client',
        version: this.clientVersion
      },
      {
        capabilities: {}
      }
    );

    // 根据配置类型创建对应的传输层
    let transport: StdioClientTransport | SSEClientTransport;
    if (config.transportType === 'sse') {
      transport = new SSEClientTransport(new URL(config.url), {});
    } else {
      let command = ((config && config.command) || '').trim();

      // node 命令需要使用用户设置的 node，这里需要特殊处理
      if (command === 'node') {
        this.logger.info('createConnection isNodeCommand');

        command = await getUserNodePath();

        this.logger.info('createConnection getNodePath:', command);
      }

      transport = new StdioClientTransport({
        command,
        args: config.args,
        env: {
          ...(process.env.PATH ? { PATH: process.env.PATH } : {}),
          ...(config.env || {})
        },
        stderr: 'pipe'
      });
    }

    // transport 配置错误处理
    // stdio 下走该逻辑
    transport.onerror = async (error) => {
      this.logger.error(`mcp server ${name} 连接错误`, error);
      const connection = this.getConnection(name);
      if (connection) {
        connection.server.status = 'disconnected';
        this.appendErrorMessage(connection, error instanceof Error ? error.message : String(error));
        this.appendFullErrorMessage(connection, error instanceof Error ? error.message : String(error));
      }
      this.onServerChange();
    };

    // transport 配置关闭处理
    // stdio 下走该逻辑
    transport.onclose = async () => {
      this.logger.info(`mcp server ${name} 连接关闭`);
      const connection = this.getConnection(name);
      if (connection) {
        connection.server.status = 'disconnected';
      }
      this.onServerChange();
    };

    // client 配置错误处理
    client.onerror = async (error) => {
      this.logger.error(`mcp server ${name} client错误`, error);
      const connection = this.getConnection(name);
      if (connection) {
        connection.server.status = 'disconnected';
        this.appendErrorMessage(connection, error instanceof Error ? error.message : String(error));
        this.appendFullErrorMessage(connection, error instanceof Error ? error.message : String(error));
      }
      this.onServerChange();
    };

    // client 配置关闭处理
    client.onclose = async () => {
      this.logger.info(`mcp server ${name} client关闭`);
      const connection = this.getConnection(name);
      if (connection) {
        connection.server.status = 'disconnected';
      }
      this.onServerChange();
    };

    // 创建并初始化连接对象
    const connection: McpConnection = {
      server: {
        name,
        config: JSON.stringify(config),
        status: config.disabled ? 'disconnected' : 'connecting',
        disabled: config.disabled,
      },
      client,
      transport,
      transportType: config.transportType,
    };

    this.logger.info(`createConnection success: ${name}`, connection);

    // 添加到连接列表
    this.connections.push(connection);

    return connection;
  }

  /**
   * 建立MCP服务器连接
   * 处理连接初始化、错误处理和工具列表获取
   *
   * @param connection - 要建立连接的MCP连接对象
   * @throws Error 当连接建立失败时抛出错误
   */
  public async establishConnection(connection: McpConnection, retry = 0): Promise<void> {
    const { name, config, disabled } = connection.server;

    if (!connection.transport || !connection.client) {
      connection.server.status = 'disconnected';
      this.onServerChange();
      return;
    }

    this.logger.info('establishConnection start', connection);

    try {
      const configObj = JSON.parse(config);

      // 处理stdio类型连接的特殊配置
      if (configObj.transportType === 'stdio') {
        await connection.transport.start();
        const stderrStream = (connection.transport as StdioClientTransport).stderr;
        if (stderrStream) {
          stderrStream.on('data', async (data: Buffer) => {
            const output = data.toString();
            const isInfoLog = !/\berror\b/i.test(output);

            if (isInfoLog) {
              this.logger.info(`Server "${name}" info:`, output);
            } else {
              this.logger.error(`Server "${name}" error:`, output);
              const conn = this.getConnection(name);
              if (conn) {
                this.appendErrorMessage(conn, output);
                this.appendFullErrorMessage(conn, output);
                this.onServerChange();
              }
            }
          });
        } else {
          this.logger.error(`No stderr stream for ${name}`);
        }

        connection.transport.start = async () => {
          // no-op
        };
      }

      // 建立实际连接
      await connection.client.connect(connection.transport);

      // 更新连接状态
      connection.server.status = 'connected';
      connection.server.error = '';
      connection.server.fullError = '';

      this.onServerChange();

      // 获取并保存工具列表
      connection.server.tools = await this.fetchToolsList(connection);
      this.logger.debug(`获取MCP Server tools ${name}`, connection.server.tools);
    } catch (error) {
      this.logger.error(`建立连接失败: ${name}:`, error);
      connection.server.status = 'disconnected';
      this.onServerChange();
      throw error;
    }
  }

  /**
   * 删除指定的MCP连接
   * 关闭连接并清理相关资源
   *
   * @param name - 要删除的连接名称
   */
  public async removeConnection(name: string): Promise<void> {
    const connection = this.getConnection(name);
    if (connection) {
      this.connections = this.connections.filter((conn) => conn.server.name !== name);
      connection.server.status = 'disconnected';
      this.onServerChange();

      try {
        await connection.transport?.close();
        await connection.client?.close();
      } catch (error) {
        this.logger.error(`删除连接失败: ${name}:`, error);
      }
    }
  }

  /**
   * 切换连接的启用/禁用状态
   * 当禁用时会关闭连接，启用时会自动建立连接
   *
   * @param name - 连接名称
   * @param disabled - 是否禁用
   * @throws Error 当连接不存在或状态切换失败时
   */
  public async toggleConnectionState(name: string, disabled: boolean): Promise<void> {
    const connection = this.getConnection(name);
    if (!connection) {
      throw new Error(`Connection ${name} not found`);
    }

    connection.server.disabled = disabled;

    if (disabled) {
      // 禁用时关闭现有连接
      await this.removeConnection(name);
      // 重新创建未连接的实例以保持配置
      const config = JSON.parse(connection.server.config);
      config.disabled = disabled;
      await this.createConnection(name, config);
    } else {
      // 启用时自动建立连接
      try {
        await this.removeConnection(name);
        const config = JSON.parse(connection.server.config);
        config.disabled = disabled;
        const newConnection = await this.createConnection(name, config);
        await this.establishConnection(newConnection);
        this.onServerChange();
      } catch (error) {
        this.logger.error(`切换连接失败: ${name}:`, error);
        this.onServerChange();
        throw error;
      }
    }
  }

  /**
   * 获取MCP服务器支持的工具列表
   *
   * @param connection - MCP连接对象
   * @returns 工具列表，如果获取失败则返回空数组
   */
  private async fetchToolsList(connection: McpConnection): Promise<McpTool[]> {
    try {
      const response = await connection.client?.request(
        {
          method: 'tools/list'
        },
        ListToolsResultSchema,
        {
          timeout: DEFAULT_REQUEST_TIMEOUT_MS
        }
      );

      const tools = (response?.tools || []).map((tool) => ({
        ...tool,
        autoApprove: false
      }));

      return tools;
    } catch (error) {
      this.logger.error(`获取工具列表失败: ${connection.server.name}:`, error);
      return [];
    }
  }

  /**
   * 添加错误信息到连接对象
   *
   * @param connection - MCP连接对象
   * @param error - 错误信息
   */
  private appendErrorMessage(connection: McpConnection, error: string) {
    // 不去聚合多次的错误信息，只返回最后一条信息
    // const newError = connection.server.error ? `${connection.server.error}\n${error}` : error;
    const newError = error;
    connection.server.error = newError;
  }

  private appendFullErrorMessage(connection: McpConnection, error: string) {
    const errorWithTime = `[${this.getNowTimeStr()}] ${error}`;
    const prev = connection.server.fullError ? connection.server.fullError.split('\n') : [];
    // 新错误插入最前面
    prev.unshift(errorWithTime);
    // 只保留前 50 条（最新的在最上面）
    const first50 = prev.slice(0, 50);
    connection.server.fullError = first50.join('\n');
  }

  private getNowTimeStr() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
}
