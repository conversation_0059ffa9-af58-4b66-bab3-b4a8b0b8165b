import { z } from 'zod';
import { McpSettingsSchema, McpSettingsSimpleSchema, ServerConfigSchema } from '../settingSchema';
import { readSettingsFile, readConfigFile, existsSettingsFile, mcpSettingDefaultContent } from '../fileHelper';
import { formatJsonError, JsonParseError, safeJsonParse } from './jsonParserHelper';
import { ValidationError } from './validationHelper';
import { ERROR_MESSAGES } from '../messages';

/**
 * MCP设置验证结果接口
 */
export interface McpSettingsValidationResult {
  isError: boolean;
  message: string;
  content: z.infer<typeof McpSettingsSimpleSchema> | undefined;
}

/**
 * 读取并验证MCP设置文件
 * @returns Promise<McpSettingsValidationResult>
 */
export async function readAndValidateMcpSettingsFile(): Promise<McpSettingsValidationResult> {
  try {
    let settingContent = await readSettingsFile();

    // 如果文件不存在，则使用默认内容
    if (!await existsSettingsFile()) {
      settingContent = mcpSettingDefaultContent;
    }

    if (!settingContent) {
      return {
        isError: true,
        message: ERROR_MESSAGES.missMcpServersError,
        content: undefined
      };
    }

    const config = safeJsonParse(settingContent);

    if (!config.success) {
       throw formatJsonError(config.error, settingContent, 'MCP设置文件');
    }

    const content = McpSettingsSimpleSchema.safeParse(config.data);

    if (!content.success) {
      return {
        isError: true,
        message:  ERROR_MESSAGES.missMcpServersError,
        content: undefined
      };
    }

    // 过滤不合法的项
    Object.keys(content.data.mcpServers).forEach(key => {
      if (key.trim() === '') {
        delete content.data.mcpServers[key];
        return;
      }

      const result = ServerConfigSchema.safeParse(content.data.mcpServers[key]);
      if (!result.success) {
        delete content.data.mcpServers[key];
      }
    });

    return {
      isError: false,
      message: '',
      content: content.data
    };
  } catch (error) {
    if (error instanceof ValidationError || error instanceof JsonParseError) {
      return {
        isError: true,
        message: error.message,
        content: undefined
      };
    }

    return {
      isError: true,
      message: ERROR_MESSAGES.readSettingsFileUnknownError,
      content: undefined
    };
  }
}

/**
 * 读取MCP配置文件
 * @returns Promise<z.infer<typeof McpSettingsSchema>>
 */
export async function readMcpConfigFile(): Promise<z.infer<typeof McpSettingsSchema>> {
  try {
    const content = await readConfigFile();
    if (!content) {
      return { mcpServers: {} };
    }

    const config = JSON.parse(content);

    if (!config.mcpServers) {
      return { mcpServers: {} };
    }

    return config;
  } catch (error) {
    return { mcpServers: {} };
  }
}

/**
 * 合并MCP配置和设置
 */
export function mergeMcpConfigAndSettings(
  settings: z.infer<typeof McpSettingsSimpleSchema>,
  config: z.infer<typeof McpSettingsSchema>
): z.infer<typeof McpSettingsSimpleSchema> {
  const mergedSettings = { ...settings };

  if (!settings.mcpServers || !config.mcpServers) {
    return settings;
  }

  for (const serverName in settings.mcpServers) {
    const configServer = config.mcpServers[serverName];
    const settingsServer = settings.mcpServers[serverName];
    if (settingsServer) {
      // 拷贝 settingsServer，去除 disabled 和 timeout 字段
      const { disabled: _sDisabled, timeout: _sTimeout, ...restSettings } = settingsServer;
      let mergedServer: Record<string, any> = { ...restSettings };

      if (configServer) {
        // 只在 configServer 里有的情况下才加
        if ('disabled' in configServer && typeof configServer.disabled !== 'undefined') {
          mergedServer.disabled = configServer.disabled;
        }
        if ('timeout' in configServer && typeof configServer.timeout !== 'undefined') {
          mergedServer.timeout = configServer.timeout;
        }
      }
      mergedSettings.mcpServers[serverName] = mergedServer;
    }
  }

  return mergedSettings;
}
