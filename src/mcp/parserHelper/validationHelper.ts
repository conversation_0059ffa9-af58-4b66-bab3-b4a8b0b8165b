import { z } from 'zod';
import { ERROR_MESSAGES } from '../messages';

/**
 * 验证错误类
 * 用于表示配置验证过程中发生的错误
 */
export class ValidationError extends Error {
  /**
   * 创建验证错误实例
   * @param {string} message - 错误消息
   * @param {ValidationErrorDetail[]} details - 错误详情列表
   * @param {unknown} [source] - 错误来源
   */
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

/**
 * 格式化验证错误
 * 将Zod验证错误转换为更友好的错误信息
 *
 * @param {z.ZodError} error - Zod错误对象
 * @param {string} context - 错误发生的上下文
 * @returns {ValidationError} 格式化后的验证错误
 */
export function formatValidationError(error: z.ZodError, context: string): ValidationError {
  return new ValidationError(ERROR_MESSAGES.formatError);
}
