import fsPromises from 'fs/promises';
import chokidar from 'chokidar';
import { getMcpConfigPath, getMcpSettingsPath } from '@/util/paths';
import { MCPLogger } from '@/util/log';

const logger = new MCPLogger(`mcp-file-helper`);

/**
 * 检查指定路径的文件是否存在
 * @param filePath - 要检查的文件路径
 * @returns Promise<boolean> 文件存在与否
 */
export async function existsFile(filePath: string) {
  try {
    await fsPromises.access(filePath, fsPromises.constants.F_OK);
    return true;
  } catch (err) {
    logger.error(`检查文件 ${filePath} 是否存在失败`, err);
    return false;
  }
}

/**
 * 确保指定路径的文件存在
 * 如果文件不存在，则使用提供的默认内容创建
 * @param filePath - 文件路径
 * @param defaultContent - 默认文件内容
 * @returns Promise<boolean> 操作是否成功
 */
export async function ensureFileExists(filePath: string, defaultContent: string = '') {
  try {
    const isExists = await existsFile(filePath);
    if (!isExists) {
      await fsPromises.writeFile(filePath, defaultContent);
    }
    return true;
  } catch (error) {
    logger.error(`确保文件 ${filePath} 存在失败`, error);
    return false;
  }
}

/**
 * 读取指定文件的内容
 * @param filePath - 要读取的文件路径
 * @returns Promise<string | null> 文件内容，读取失败时返回null
 */
export async function readFile(filePath: string) {
  try {
    const content = await fsPromises.readFile(filePath, 'utf-8');
    return content;
  } catch (error) {
    logger.error(`读取文件 ${filePath} 失败`, error);
    return null;
  }
}

/**
 * 写入内容到指定文件
 * @param filePath - 目标文件路径
 * @param content - 要写入的内容
 * @returns Promise<boolean> 写入操作是否成功
 */
export async function writeFile(filePath: string, content: string) {
  try {
    await fsPromises.writeFile(filePath, content);
    return true;
  } catch (error) {
    logger.error(`写入文件 ${filePath} 失败`, error);
    return false;
  }
}

/**
 * 监听指定文件的变化
 * 使用chokidar库实现文件变化监听，支持原子写入和写入完成等待
 *
 * @param filePath - 要监听的文件路径
 * @param callback - 文件变化时的回调函数
 * @returns () => void 用于停止监听的函数
 */
export function watchFile(filePath: string, callback: () => void) {
  // 监听文件变化
  const watcher = chokidar.watch(filePath, {
    awaitWriteFinish: false,
    atomic: 300,
    ignoreInitial: true
  });

  watcher.on('all', (error) => {
    callback();
  });

  return () => {
    watcher.close();
  };
}

// #region MCP设置文件

/**
 * 检查MCP设置文件是否存在
 * @returns boolean 文件存在与否
 */
export async function existsSettingsFile() {
  return await existsFile(getMcpSettingsPath());
}

/**
 * 检查并确保MCP设置文件存在
 * 如果文件不存在，则使用默认内容创建
 * @returns Promise<string> MCP设置文件的完整路径
 * @throws Error 当文件创建失败时
 */
export async function checkMcpSettingsFile() {
  const filePath = getMcpSettingsPath();

  if (!(await ensureFileExists(filePath, mcpSettingDefaultContent))) {
    throw new Error('mcp setting 文件创建失败！');
  }

  return filePath;
}

/**
 * 写入内容到MCP设置文件
 * @param content - 要写入的设置内容
 * @returns Promise<boolean> 写入操作是否成功
 */
export async function writeSettingsFile(content: string) {
  return await writeFile(getMcpSettingsPath(), content);
}

/**
 * 读取MCP设置文件内容
 * @returns Promise<string | null> 设置文件内容，读取失败时返回null
 */
export async function readSettingsFile(): Promise<string | null> {
  return await readFile(getMcpSettingsPath());
}

// #endregion MCP设置文件

// #region MCP配置文件

/**
 * 写入内容到MCP配置文件
 * @param content - 要写入的配置内容
 * @returns Promise<boolean> 写入操作是否成功
 */
export async function writeConfigFile(content: string) {
  return await writeFile(getMcpConfigPath(), content);
}

/**
 * 读取MCP配置文件内容
 * @returns Promise<string | null> 配置文件内容，读取失败时返回null
 */
export async function readConfigFile() {
  return await readFile(getMcpConfigPath());
}

// #endregion MCP配置文件

/**
 * MCP设置文件的默认内容
 * 提供一个空的服务器配置对象
 */
export const mcpSettingDefaultContent = `{
  "mcpServers": {
    
  }
}
`;
