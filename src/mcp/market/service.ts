import { ResponseBase, STATUS } from '@/protocol/index.d';
import { MarketMcpDetail } from "../types";
import {
  fetchAvailableMcpListByMarketApi,
  fetchMcpDetailByMarketApi,
} from './api';
import { MCPLogger } from '@/util/log';

const logger = new MCPLogger(`mcp-file-helper`);

/**
 * 分页获取市场可用的 MCP 列表
 *
 * @param params - 查询参数，包括页码、每页数量和搜索关键字
 * @returns Promise<ResponseBase<{ page, pageSize, total, records }>>
 *   - status: 操作状态
 *   - code: 状态码
 *   - message: 提示信息
 *   - data: 返回的分页数据
 *
 * 异常处理：
 *   - 如果接口返回 code !== 0 或发生异常，返回 FAILED 状态和错误信息
 */
export async function fetchAvailableMcpListByMarket(params: {
  page?: number;
  pageSize?: number;
  searchKeyword?: string;
}): Promise<ResponseBase<{
  page?: number;
  pageSize?: number;
  total?: number;
  records?: MarketMcpDetail[];
}>> {
  try {
    const data = await fetchAvailableMcpListByMarketApi(params);

    // code 非 0 视为失败
    if (data.code !== 0) {
      throw new Error(data.message || 'Market API error');
    }
    return {
      status: STATUS.OK,
      code: data.code,
      message: data.message,
      data: data.data
    };
  } catch (e: any) {
    logger.error('fetchAvailableMcpListByMarket error', e);
    // 捕获异常，返回 FAILED 状态
    return {
      status: STATUS.FAILED,
      code: -1,
      message: e?.message || 'Unknown error',
      data: undefined
    };
  }
}

/**
 * 获取指定 serverId 的 MCP 详情
 *
 * @param params - 包含 serverId 的对象
 * @returns Promise<ResponseBase<MarketMcpDetail>>
 *   - status: 操作状态
 *   - code: 状态码
 *   - message: 提示信息
 *   - data: MCP 详情数据
 *
 * 异常处理：
 *   - 如果接口返回 code !== 0 或发生异常，返回 FAILED 状态和错误信息
 */
export async function fetchMcpDetailByMarket(params: { serverId: string; }): Promise<ResponseBase<MarketMcpDetail>> {
  try {
    const data = await fetchMcpDetailByMarketApi(params);
    // code 非 0 视为失败
    if (data.code !== 0) {
      throw new Error(data.message || 'Market API error');
    }
    return {
      status: STATUS.OK,
      code: 0,
      message: data.message,
      data: data.data
    };
  } catch (e: any) {
    logger.error('fetchMcpDetailByMarket error', e);
    // 捕获异常，返回 FAILED 状态
    return {
      status: STATUS.FAILED,
      code: -1,
      message: e?.message || 'Unknown error',
      data: undefined
    };
  }
}
