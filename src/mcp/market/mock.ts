export const fetchAvailableMcpListByMarketMockData = {
    code: 0,
    message: "success",
    data: {
        page: 1,
        pageSize: 10,
        total: 100,
        records: [{
            serverName: "kdev-api",
            serverId: "123456",
            serverType: "MCP",
            serverDescription: "This is a test MCP server",
            serverIntroductionMarkdown: "This is a test MCP server",
            internalProvide: true,
            serverConfig: {
                installationsArgument: [],
                installationsTemplate: {
                    "kdev-api": {
                        "command": "npx",
                        "args": [
                            "-y",
                            "@ks-dmo/kdev-api-mcp-server@1.1.1-bata.1"
                        ]
                    },
                }
            }
        },
        {
            serverName: "github",
            serverId: "123456",
            serverType: "MCP",
            serverDescription: "This is a test MCP server",
            serverIntroductionMarkdown: "This is a test MCP server",
            internalProvide: false,
            serverConfig: {
                installationsArgument: [
                    {
                        description: "GitHub Personal Access Token",
                        key: "GITHUB_PERSONAL_ACCESS_TOKEN",
                        title: "GitHub Personal Access Token",
                        required: true,
                        type: "string",
                        placeholder: "GitHub Personal Access Token",
                        defaultValue: "",
                    }
                ],
                installationsTemplate: {
                    "github": {
                        "command": "npx",
                        "args": [
                            "-y",
                            "@modelcontextprotocol/server-github"
                        ],
                        "env": {
                            "GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_PERSONAL_ACCESS_TOKEN}"
                        }
                    }
                }
            }
        }
        ],
    }
}

export const fetchMcpDetailByMarketMockData = {
    code: 0,
    message: "success",
    data: {
        serverName: "kdev-api",
        serverId: "123456",
        serverType: "MCP",
        serverDescription: "This is a test MCP server",
        serverIntroductionMarkdown: "This is a test MCP server",
        internalProvide: true,
        serverConfig: {
            installationsArgument: [],
            installationsTemplate: {
                "kdev-api": {
                    "command": "npx",
                    "args": [
                        "-y",
                        "@ks-dmo/kdev-api-mcp-server@1.1.1-bata.1"
                    ]
                },
            }
        }
    }
} 