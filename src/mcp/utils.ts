import which from 'which';

/**
 * 深度比较两个值是否相等
 */
export function deepEqual(a: any, b: any): boolean {
  // 类型不同，直接返回false
  if (typeof a !== typeof b) {
    return false;
  }
  // 对象类型，转换为JSON字符串比较
  if (typeof a === 'object' && a !== null) {
    return JSON.stringify(a) === JSON.stringify(b);
  }
  // 基本类型直接比较
  return a === b;
}

/**
 * 将秒转换为毫秒
 * 用于将配置中的超时时间（秒）转换为毫秒单位
 *
 * @param seconds - 秒数
 * @returns number 对应的毫秒数
 *
 * @example
 * secondsToMs(5) // 5000
 */
export function secondsToMs(seconds: number): number {
  return seconds * 1000;
}

/**
 * 获取node路径
 * 如果node路径不存在，则返回'node'
 * @returns string
 */
export async function getUserNodePath() {
  try {
    let cmdPath = await which('node');
    return cmdPath || 'node';
  } catch (error) {
    return 'node';
  }
}
