import { Logger } from '@/util/log';

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

export class CacheManager<T> {
  private readonly logger: Logger = new Logger('CacheManager');
  private readonly cache: Map<string, CacheEntry<T>> = new Map();

  constructor(
    private readonly maxSize: number,
    private readonly ttl: number,
    private readonly name: string
  ) {}

  /**
   * 获取缓存大小
   */
  public get size(): number {
    return this.cache.size;
  }

  /**
   * 获取缓存项
   */
  public get(key: string): T | undefined {
    const entry = this.cache.get(key);
    if (!entry) return undefined;

    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return undefined;
    }

    return entry.data;
  }

  /**
   * 设置缓存项
   */
  public set(key: string, value: T): void {
    this.cache.set(key, {
      data: value,
      timestamp: Date.now()
    });
    this.cleanup();
  }

  /**
   * 检查缓存项是否存在且有效
   */
  public has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * 删除缓存项
   */
  public delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * 获取所有缓存项的迭代器
   */
  public entries(): IterableIterator<[string, CacheEntry<T>]> {
    return this.cache.entries();
  }

  /**
   * 清理过期和超量的缓存项
   */
  public cleanup(): void {
    const now = Date.now();
    
    // 清理过期项
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(key);
      }
    }

    // 如果缓存太大，删除最旧的条目
    if (this.cache.size > this.maxSize) {
      const sortedEntries = Array.from(this.cache.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp);
      const entriesToDelete = sortedEntries.slice(0, sortedEntries.length - this.maxSize);
      
      for (const [key] of entriesToDelete) {
        this.cache.delete(key);
      }
      
      this.logger.info(`Cleaned up ${entriesToDelete.length} old entries from ${this.name} cache`);
    }
  }

  /**
   * 检查缓存项是否过期
   */
  private isExpired(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp > this.ttl;
  }
} 