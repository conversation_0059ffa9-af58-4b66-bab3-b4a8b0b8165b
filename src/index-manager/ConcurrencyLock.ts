import { getKwaipilotGlobalPath } from '@/util/paths';
import { createHash } from 'crypto';
import path from 'path';
import { Logger } from '@/util/log';
import { lock } from 'proper-lockfile';
import fs from 'fs';
import { AGENT_NAMESPACE } from '@/util/const';

/**
 * 并发锁
 *
 * 这个类提供了一个基于文件的并发锁机制，用于保证在不同进程或线程之间的互斥操作。
 * 特点：
 * 1. 在同一个线程中可以重复获取锁（可重入）
 * 2. 在不同线程中获取会互斥，保证资源的安全访问
 * 3. 使用单例模式确保在应用中只有一个锁管理器实例
 * 4. 支持自动续租和过期机制，防止因进程崩溃导致的死锁
 */
class ConcurrencyLock {
  /**
   * 缓存目录路径到MD5哈希值的映射，用于快速查找
   */
  private lockMap: Map<string, string> = new Map();

  /**
   * 日志记录器
   */
  private logger: Logger = new Logger('ConcurrencyLock');

  /**
   * 当前锁的状态
   * true: 已获取锁
   * false: 未获取锁
   */
  private lockState: boolean = false;

  /**
   * 单例实例
   */
  private static instance: ConcurrencyLock;

  /**
   * 私有构造函数，防止外部直接创建实例
   */
  constructor() {
    this.lockMap = new Map();
  }

  /**
   * 获取ConcurrencyLock的单例实例
   * @returns ConcurrencyLock实例
   */
  static getInstance() {
    if (!this.instance) {
      this.instance = new ConcurrencyLock();
    }
    return this.instance;
  }

  /**
   * 计算目录路径的MD5哈希值
   * @param dirPath 目录路径
   * @returns MD5哈希值，如果dirPath为空则返回空字符串
   */
  private getLockMd5 = (dirPath: string) => {
    if (!dirPath) {
      return '';
    }
    if (this.lockMap.has(dirPath)) {
      return this.lockMap.get(dirPath);
    }
    const md5 = createHash('md5').update(dirPath).digest('hex');
    this.lockMap.set(dirPath, md5);
    return md5;
  };

  /**
   * 根据目录路径获取锁文件的完整路径
   * @param dirPath 目录路径
   * @returns 锁文件的完整路径，如果dirPath为空则返回空字符串
   */
  private getLockFileName = (dirPath: string) => {
    if (!dirPath) {
      return '';
    }
    const lockDir = path.join(getKwaipilotGlobalPath(), 'lock');
    if (!fs.existsSync(lockDir)) {
      fs.mkdirSync(lockDir, { recursive: true });
    }
    return path.join(lockDir, `${this.getLockMd5(dirPath)}_index`);
  };

  /**
   * 获取锁
   *
   * 为指定目录创建并获取一个文件锁，实现互斥访问
   * 该方法具有可重入性，同一线程再次调用时会立即返回成功
   *
   * @param dirPath 目录路径
   * @returns 是否获取锁成功
   * @throws 如果锁定过程中发生错误
   */
  acquireLock = async (dirPath: string): Promise<void> => {
    if (!dirPath) {
      this.logger.error('dirPath is required');
      // 如果 dirpath 不存在，则未打开项目，无法进行索引。
      throw new Error('dirPath is required');
    }
    // 重入性检查：如果当前线程已持有锁，则直接返回成功
    if (this.lockState) {
      this.logger.info('当前线程已持有锁');
      // 当前进程持有进程锁，直接返回
      return;
    }
    // 获取锁的名称
    const lockPath = this.getLockFileName(dirPath);

    try {
      // 通过文件系统加锁，实现跨进程/线程的互斥
      await lock(lockPath, {
        stale: 15000, // 锁过期时间：15秒
        update: 3000, // 每3秒自动续租
        realpath: false
      });
      // 记录当前进程锁的占用状态，下次同进程调用时会直接 return
      this.lockState = true;
      this.logger.info('获取锁成功' + dirPath + process.pid);
    } catch (error) {
      this.logger.error('获取锁失败, 有其他进程持有锁', error);
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'LOCK_CONFLICT',
        millis: 1,
        extra4: dirPath
      });
      throw error;
    }
  };
}

export default ConcurrencyLock.getInstance();
