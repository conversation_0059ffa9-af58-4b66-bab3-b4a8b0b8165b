import { exec } from 'child_process';
import { promisify } from 'util';
import { Logger } from '@/util/log';
import { IndexTaskAction } from '@/index-manager/types';
import * as fs from 'fs';
import * as path from 'path';
import ignore from 'ignore';
import { getKwaipilotIgnoreFiles } from '@/util/file';

const execAsync = promisify(exec);
// Define a larger buffer size (20MB)
const MAX_BUFFER = 1024 * 1024 * 20;

export class GitManager {
  private readonly logger: Logger = new Logger('GitManager');

  /**
   * 获取仓库所有文件列表
   */
  public async getAllFiles(dirPath: string): Promise<string[]> {
    try {
      // 获取所有文件（包括未追踪的文件）
      const { stdout: lsFiles } = await execAsync(`git -C ${dirPath} ls-files --others --exclude-standard`, {
        maxBuffer: MAX_BUFFER
      });
      const untrackedFiles = lsFiles.trim() ? lsFiles.trim().split('\n') : [];

      // 获取已追踪的文件
      const { stdout: trackedFiles } = await execAsync(`git -C ${dirPath} ls-tree -r --name-only HEAD`, {
        maxBuffer: MAX_BUFFER
      });
      const trackedFileList = trackedFiles.trim() ? trackedFiles.trim().split('\n') : [];

      // 合并去重
      const allFiles = [...new Set([...untrackedFiles, ...trackedFileList])];
      const ig = ignore().add(getKwaipilotIgnoreFiles(dirPath));
      // 过滤掉已经不存在的文件
      const existingFiles = await Promise.all(
        allFiles.map(async (file) => {
          try {
            await fs.promises.access(path.join(dirPath, file));
            if (ig.ignores(file)) {
              return null;
            }
            return file;
          } catch {
            return null;
          }
        })
      );

      return existingFiles.filter((file): file is string => file !== null);
    } catch (error) {
      this.logger.error(`Error getting all files from git: ${error}`);
      return [];
    }
  }

  /**
   * 获取两个提交之间的变更文件
   */
  public async getChangedFiles(
    dirPath: string,
    newCommitId: string,
    oldCommitId: string
  ): Promise<Array<{ path: string; status: IndexTaskAction }>> {
    try {
      process.chdir(dirPath);

      const { stdout } = await execAsync(`git diff --name-status ${oldCommitId} ${newCommitId}`, {
        maxBuffer: MAX_BUFFER
      });
      const ig = ignore().add(getKwaipilotIgnoreFiles(dirPath));
      return stdout
        .trim()
        .split('\n')
        .filter((line) => !ig.ignores(line))
        .map((line) => {
          const [status, path] = line.split('\t');
          let fileStatus: IndexTaskAction = 'modify';

          switch (status.charAt(0)) {
            case 'A':
              fileStatus = 'create';
              break;
            case 'M':
              fileStatus = 'modify';
              break;
            case 'D':
              fileStatus = 'delete';
              break;
            default:
              fileStatus = 'modify';
          }

          return { path, status: fileStatus };
        });
    } catch (error) {
      this.logger.error(`Error getting changed files: ${error}`);
      throw error;
    }
  }

  /**
   * 判断目录是否为 git 仓库
   */
  public async isGitRepo(dirPath: string): Promise<boolean> {
    try {
      // 方法1：检测 .git 目录是否存在
      const gitDir = path.join(dirPath, '.git');
      if (
        await fs.promises
          .stat(gitDir)
          .then((stat) => stat.isDirectory())
          .catch(() => false)
      ) {
        return true;
      }
      // 方法2：通过 git 命令判断
      const { stdout } = await execAsync(`git -C ${dirPath} rev-parse --is-inside-work-tree`);
      return stdout.trim() === 'true';
    } catch {
      return false;
    }
  }
}
