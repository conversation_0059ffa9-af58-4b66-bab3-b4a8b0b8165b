import { IProtocol } from '@/protocol';
import { I<PERSON><PERSON>eng<PERSON>, Message } from '@/protocol/messenger';
import { Logger } from '@/util/log';
import { AGENT_NAMESPACE } from '@/util/const';
import net from 'net';
import { v4 as uuidv4 } from 'uuid';
import { BufferDataHandle } from '@/util/messenger';

export class TcpMessenger<ToProtocol extends IProtocol, FromProtocol extends IProtocol>
  implements IMessenger<ToProtocol, FromProtocol> {
  private port: number = process.env.KWAIPILOT_AGENT_PORT ? parseInt(process.env.KWAIPILOT_AGENT_PORT) : 3000;
  private host: string = '127.0.0.1';
  private socket: net.Socket | null = null;
  private logger = new Logger('TcpMessenger');
  private readonly dataHandler = new BufferDataHandle();

  typeListeners = new Map<keyof ToProtocol, ((message: Message) => any)[]>();
  idListeners = new Map<string, (message: Message) => any>();
  isConnected = false;
  isInited = false;
  requestPendding: {
    messageType: keyof FromProtocol;
    data: FromProtocol[keyof FromProtocol][0];
    messageId: string;
  }[] = [];
  responsePendding: string[] = [];
  setIsInited(isInited: boolean) {
    this.isInited = isInited;
    for (const data of this.responsePendding) {
      this._handleLine(data);
    }
    this.responsePendding = [];
  }
  constructor() {
    const server = net.createServer();
    server.on('connection', (socket) => {
      this.socket = socket;
      this.isConnected = true;
      this.logger.info('Connected to server');
      this.requestPendding.forEach((request) => {
        this.send(request.messageType, request.data, request.messageId);
      });
      this.requestPendding = [];

      socket.on('data', (data: Buffer) => {
        this.logger.info(`tcp data in---- ${data.toString()}`);

        this._handleData(data);
      });

      socket.on('end', () => {
        this.logger.info('Disconnected from server');
      });

      socket.on('error', (err: any) => {
        this.logger.error('Client error:', err);
      });
    });

    server.listen(this.port, this.host, () => {
      this.logger.info(`Server listening on port ${this.port}`);
    });
  }

  private _onErrorHandlers: ((error: Error) => void)[] = [];

  onError(handler: (error: Error) => void) {
    this._onErrorHandlers.push(handler);
  }

  public async awaitConnection() {
    while (!this.socket) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
  }

  private _handleLine(line: string) {
    try {
      this.logger.info(`tcp data in---- ${line}`);
      const msg: Message = JSON.parse(line);
      if (msg.messageType === undefined || msg.messageId === undefined) {
        throw new Error('Invalid message sent: ' + JSON.stringify(msg));
      }
      const skipType = ['config/getIdeSetting', 'state/ideState', 'state/ideInfo'];
      // 项目未初始化完成，并且消息类型不是 config/getIdeSetting 或 state/ideState，则将消息缓存
      if (!this.isInited && !skipType.includes(msg.messageType)) {
        this.responsePendding.push(line);
        return;
      }

      // Call handler and respond with return value
      const listeners = this.typeListeners.get(msg.messageType as any);
      listeners?.forEach(async (handler) => {
        try {
          const response = await handler(msg);
          if (response && typeof response[Symbol.asyncIterator] === 'function') {
            for await (const update of response) {
              this.send(msg.messageType, update, msg.messageId);
            }
            this.send(msg.messageType, { done: true }, msg.messageId);
          } else {
            this.send(msg.messageType, response || {}, msg.messageId);
          }
        } catch (e: any) {
          this.logger.warn(`Error running handler for "${msg.messageType}": `, e);
          this._onErrorHandlers.forEach((handler) => {
            handler(e);
          });
          this.send(msg.messageType, { status: 'error', message: e.message }, msg.messageId);
        }
      });

      // Call handler which is waiting for the response, nothing to return
      this.idListeners.get(msg.messageId)?.(msg);
    } catch (e) {
      let truncatedLine = line;
      if (line.length > 200) {
        truncatedLine = line.substring(0, 100) + '...' + line.substring(line.length - 100);
      }
      this.logger.error('Error parsing line: ', truncatedLine, e);
      return;
    }
  }

  private _handleData(data: Buffer) {
    this.dataHandler.handleData(data, line => this._handleLine(line));
  }

  send<T extends keyof FromProtocol>(messageType: T, data: FromProtocol[T][0], messageId?: string): string {
    messageId = messageId ?? uuidv4();
    const msg: Message = {
      messageType: messageType as string,
      data,
      messageId,
    };

    this.socket?.write(JSON.stringify(msg) + '\r\n');
    return messageId;
  }

  on<T extends keyof ToProtocol>(
    messageType: T,
    handler: (message: Message<ToProtocol[T][0]>) => ToProtocol[T][1]
  ): void {
    if (!this.typeListeners.has(messageType)) {
      this.typeListeners.set(messageType, []);
    }
    this.typeListeners.get(messageType)?.push(handler);
  }

  invoke<T extends keyof ToProtocol>(messageType: T, data: ToProtocol[T][0]): ToProtocol[T][1] {
    return this.typeListeners.get(messageType)?.[0]?.({
      messageId: uuidv4(),
      messageType: messageType as string,
      data,
    });
  }
  request<T extends keyof FromProtocol>(messageType: T, data: FromProtocol[T][0]): Promise<FromProtocol[T][1]> {
    const start = Date.now();
    const messageId = uuidv4();
    return new Promise((resolve) => {
      const handler = (msg: Message) => {
        resolve(msg.data);
        this.idListeners.delete(messageId);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: messageType as string,
          millis: Date.now() - start,
        });
      };
      this.idListeners.set(messageId, handler);
      if (this.isConnected) {
        this.send(messageType, data, messageId);
      } else {
        this.logger.error('Not connected to server');
        this.requestPendding.push({ messageType, data, messageId });
      }
    });
  }
}
