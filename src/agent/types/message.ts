import { type ToolUse, ToolResponse, Ask } from './type.d';

export { ToolUse } from './type.d';

export type AssistantMessageContent = TextContent | ToolUse;

export type PushToolResult = (content: ToolResponse) => void;

export type RemoveClosingTag = (tag: ToolParamName, content?: string) => string;

export type ReportToolAction = (duration: number, params: Record<string, any>) => void;

export type HandleError = (action: string, error: Error, toolName: ToolUseName) => Promise<void>;

export type AskApproval = (type: Ask, partialMessage?: string) => Promise<boolean>;

export interface TextContent {
  type: 'text';
  content: string;
  partial: boolean;
}

export const toolUseNames = [
  'execute_command',
  'read_file',
  'edit_file',
  'replace_in_file',
  'write_to_file',
  'codebase_search',
  'grep_search',
  'list_files',
  // "list_code_definition_names",
  'use_mcp_tool',
  'ask_followup_question'
] as const;

// Converts array of tool call names into a union type ("execute_command" | "read_file" | ...)
export type ToolUseName = (typeof toolUseNames)[number];

export const toolParamNames = [
  'command',
  'is_background',
  'requires_approval',
  'target_file',
  'code_edit',
  'instructions',
  'query',
  'target_directories',
  'description',
  'path',
  'should_read_entire_file',
  'end_line_one_indexed_inclusive',
  'start_line_one_indexed',
  'content',
  'diff',
  'regex',
  'file_pattern',
  'recursive',
  'action',
  'url',
  'coordinate',
  'text',
  'tool_name',
  'arguments',
  'question',
  'response',
  'result',
  'language',
  'server_name',
  'search',
  'replace',
  'replacements',
  'start_line',
  'end_line',
  'line_count'
] as const;

export type ToolParamName = (typeof toolParamNames)[number];

export interface ExecuteCommandToolUse extends ToolUse {
  name: 'execute_command';
  // Pick<Record<ToolParamName, string>, "command"> makes "command" required, but Partial<> makes it optional
  params: Partial<Pick<Record<ToolParamName, string>, 'command' | 'requires_approval'>>;
}

export interface ReadFileToolUse extends ToolUse {
  name: 'read_file';
  params: Partial<Pick<Record<ToolParamName, string>, 'path'>>;
}

export interface EditFileToolUse extends ToolUse {
  name: 'edit_file';
  params: Partial<Pick<Record<ToolParamName, string>, 'content' | 'instructions' | 'language' | 'path'>>;
}

export interface SearchAndReplaceToolUse extends ToolUse {
  name: 'replace_in_file';
  params: Required<Pick<Record<ToolParamName, string>, 'path' | 'replacements'>>;
}
export interface WriteToFileToolUse extends ToolUse {
  name: 'write_to_file';
  params: Partial<Pick<Record<ToolParamName, string>, 'path' | 'content' | 'line_count'>>;
}

export interface CodebaseSearchFilesToolUse extends ToolUse {
  name: 'codebase_search';
  params: Partial<Pick<Record<ToolParamName, string>, 'query' | 'target_directories'>>;
}
export interface GrepSearchFilesToolUse extends ToolUse {
  name: 'grep_search';
  params: Partial<Pick<Record<ToolParamName, string>, 'path' | 'regex' | 'file_pattern'>>;
}

export interface ListFilesToolUse extends ToolUse {
  name: 'list_files';
  params: Partial<Pick<Record<ToolParamName, string>, 'path' | 'recursive'>>;
}

export interface AskFollowupQuestionToolUse extends ToolUse {
  name: 'ask_followup_question';
  params: Partial<Pick<Record<ToolParamName, string>, 'question'>>;
}
