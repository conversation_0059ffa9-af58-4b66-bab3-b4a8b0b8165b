import { Logger } from '@/util/log';
import { DiffSet } from '../../types/type';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { GlobalConfig } from '@/util/global';
import { IdeCommonMessage, Message } from '@/protocol/messenger';
import CheckpointTracker from './CheckpointTracker';
import pTimeout from 'p-timeout';
import { getTrace } from '@/util/langfuse';
const logger = new Logger('assistant-agent');

export const getDiffSet = async (
  msg: Message<{ sessionId: string; lhsHash: string; rhsHash?: string }>
): Promise<DiffSet[]> => {
  logger.info(`assistant/agent/getDiffSet, ${JSON.stringify(msg)}`);
  logger.perf({
    namespace: ASSISTANT_NAMESPACE,
    subtag: 'assistant/agent/getDiffSet',
    extra3: GlobalConfig.getConfig().getPlatform(),
    extra4: (msg.common as IdeCommonMessage)?.cwd
  });
  const data = msg.data;
  const cwd = (msg.common as IdeCommonMessage)?.cwd || process.cwd();
  const { sessionId, lhsHash, rhsHash } = data;
  const trace = getTrace(sessionId, GlobalConfig?.getConfig()?.getUsername());
  const traceCheckpointDiff = trace?.generation({
    name: 'checkpoint_diff',
    input: {
      sessionId,
      lhsHash,
      rhsHash
    }
  });
  let checkpointTracker: CheckpointTracker | undefined;
  try {
    checkpointTracker = await pTimeout(CheckpointTracker.create(sessionId, cwd), {
      milliseconds: 15_000,
      message:
        'Checkpoints taking too long to initialize. Consider re-opening Kwaipilot in a project that uses git, or disabling checkpoints.'
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    traceCheckpointDiff?.end({
      output: { status: 'failed', error: `Failed to initialize checkpoint tracker: ${errorMessage}` }
    });
    console.error('Failed to initialize checkpoint tracker:', errorMessage);
  }
  if (lhsHash && checkpointTracker) {
    try {
      const diffSet = await checkpointTracker?.getDiffSet(lhsHash, rhsHash);
      traceCheckpointDiff?.end({
        output: { diffSet }
      });
      return diffSet;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      traceCheckpointDiff?.end({
        output: { status: 'failed', error: errorMessage }
      });
      return [];
    }
  } else {
    return [];
  }
};
