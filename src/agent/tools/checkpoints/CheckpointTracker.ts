import fs from 'fs/promises';
import * as path from 'path';
import simpleGit, { SimpleGit } from 'simple-git';
import { GitOperations } from './CheckpointGitOperations';
import {
  getShadowGitPath,
  getShadowGitPathWithSessionId,
  getWorkingDirectory,
  hashWorkingDir
} from './CheckpointUtils';
import { getKwaipilotGlobalPath } from '@/util/paths';
import { AgentLogger } from '@/util/log';

class CheckpointTracker {
  private sessionId: string;
  private cwd: string;
  private cwdHash: string;
  private globalStoragePath: string;
  // 缓存最后检索的影子 Git 仓库的工作树配置值
  private lastRetrievedShadowGitConfigWorkTree?: string;
  private gitOperations: GitOperations;
  private readonly agentTraceLogger: InstanceType<typeof AgentLogger>;

  private cleanCommitHash(hash: string): string {
    return hash.startsWith('HEAD ') ? hash.slice(5) : hash;
  }

  /**
   * Creates a new CheckpointTracker instance to manage checkpoints for a specific task.
   * The constructor is private - use the static create() method to instantiate.
   *
   * @param sessionId - Unique identifier for the task being tracked
   * @param cwd - The current working directory to track files in
   * @param cwdHash - Hash of the working directory path for shadow git organization
   */
  private constructor(globalStoragePath: string, sessionId: string, cwd: string, cwdHash: string) {
    this.globalStoragePath = globalStoragePath;
    this.sessionId = sessionId;
    this.cwd = cwd;
    this.cwdHash = cwdHash;
    this.gitOperations = new GitOperations(cwd);
    this.agentTraceLogger = new AgentLogger(`s-${this.sessionId}`);
  }

  public static async create(sessionId: string, cwd: string): Promise<CheckpointTracker | undefined> {
    try {
      const globalStoragePath = getKwaipilotGlobalPath();

      // Check if checkpoints are disabled in VS Code settings
      // const enableCheckpoints = vscode.workspace.getConfiguration("kwaipilot").get<boolean>("enableCheckpoints") ?? true
      // if (!enableCheckpoints) {
      // 	return undefined // Don't create tracker when disabled
      // }

      // Check if git is installed by attempting to get version
      try {
        await simpleGit().version();
      } catch (error) {
        throw new Error('Git must be installed to use checkpoints.'); // FIXME: must match what we check for in TaskHeader to show link
      }

      const workingDir = await getWorkingDirectory(cwd);
      const cwdHash = hashWorkingDir(workingDir);

      // {globalStoragePath}/checkpoints/{cwdHash}/
      const newTracker = new CheckpointTracker(globalStoragePath, sessionId, workingDir, cwdHash);
      newTracker.agentTraceLogger.debug(`Repository ID (cwdHash): ${cwdHash}`);

      const gitPath = await getShadowGitPath(newTracker.globalStoragePath, newTracker.sessionId, newTracker.cwdHash);
      await newTracker.gitOperations.initShadowGit(gitPath, workingDir, sessionId);
      return newTracker;
    } catch (error) {
      console.error('Failed to create CheckpointTracker:', error);
      throw error;
    }
  }
  /**
   * Creates a new checkpoint commit in the shadow git repository.
   *
   * Key behaviors:
   * - Creates commit with checkpoint files in shadow git repo
   * - Caches the created commit hash
   *
   * Commit structure:
   * - Commit message: "checkpoint-{cwdHash}-{sessionId}"
   * - Always allows empty commits
   *
   * Dependencies:
   * - Requires initialized shadow git (getShadowGitPath)
   * - Uses addCheckpointFiles to stage changes using 'git add .'
   * - Relies on git's native exclusion handling via the exclude file
   *
   * @returns Promise<string | undefined> The created commit hash, or undefined if:
   * - Shadow git access fails
   * - Staging files fails
   * - Commit creation fails
   * @throws Error if unable to:
   * - Access shadow git path
   * - Initialize simple-git
   * - Stage or commit files
   */
  public async commit(): Promise<string | undefined> {
    try {
      const gitPath = await getShadowGitPath(this.globalStoragePath, this.sessionId, this.cwdHash);
      const git = simpleGit(path.dirname(gitPath));

      const addFilesResult = await this.gitOperations.addCheckpointFiles(git);
      if (!addFilesResult.success) {
        // console.error('Failed to add at least one file(s) to checkpoints shadow git');
        this.agentTraceLogger.debug(`commit: Failed to add at least one file(s) to checkpoints shadow git`);
      }

      const commitMessage = 'checkpoint-' + this.cwdHash + '-' + this.sessionId;
      const result = await git.commit(commitMessage, {
        '--allow-empty': null,
        '--no-verify': null
      });
      const commitHash = (result.commit || '').replace(/^HEAD\s+/, '');
      return commitHash;
    } catch (error) {
      // console.error('Failed to create checkpoint:', {
      //   sessionId: this.sessionId,
      //   error
      // });
      this.agentTraceLogger.debug(
        `commit: Failed to create checkpoint: ${error instanceof Error ? error.message : String(error)}`
      );
      throw new Error(`Failed to create checkpoint: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Retrieves the worktree path from the shadow git configuration.
   * The worktree path indicates where the shadow git repository is tracking files,
   * which should match the current workspace directory.
   *
   * Key behaviors:
   * - Caches result in lastRetrievedShadowGitConfigWorkTree to avoid repeated reads
   * - Returns cached value if available
   * - Reads git config if no cached value exists
   *
   * Configuration read:
   * - Uses simple-git to read core.worktree config
   * - Operates on shadow git at path from getShadowGitPath()
   *
   * @returns Promise<string | undefined> The configured worktree path, or undefined if:
   * - Shadow git repository doesn't exist
   * - Config read fails
   * - No worktree is configured
   * @throws Error if unable to:
   * - Access shadow git path
   * - Initialize simple-git
   * - Read git configuration
   */
  public async getShadowGitConfigWorkTree(): Promise<string | undefined> {
    if (this.lastRetrievedShadowGitConfigWorkTree) {
      return this.lastRetrievedShadowGitConfigWorkTree;
    }
    try {
      const gitPath = await getShadowGitPath(this.globalStoragePath, this.sessionId, this.cwdHash);
      this.lastRetrievedShadowGitConfigWorkTree = await this.gitOperations.getShadowGitConfigWorkTree(gitPath);
      return this.lastRetrievedShadowGitConfigWorkTree;
    } catch (error) {
      // console.error('Failed to get shadow git config worktree:', error);
      this.agentTraceLogger.debug(`getShadowGitConfigWorkTree: Failed to get shadow git config worktree: ${error}`);
      return undefined;
    }
  }

  /**
   * Resets the shadow git repository's HEAD to a specific checkpoint commit.
   * This will discard all changes after the target commit and restore the
   * working directory to that checkpoint's state.
   *
   * Dependencies:
   * - Requires initialized shadow git (getShadowGitPath)
   * - Must be called with a valid commit hash from this task's history
   *
   * @param commitHash - The hash of the checkpoint commit to reset to
   * @returns Promise<void> Resolves when reset is complete
   * @throws Error if unable to:
   * - Access shadow git path
   * - Initialize simple-git
   * - Reset to target commit
   */
  public async resetHead(commitHash: string): Promise<void> {
    this.agentTraceLogger.info(`Resetting to checkpoint: ${commitHash}`);

    try {
      const gitPath = await getShadowGitPath(this.globalStoragePath, this.sessionId, this.cwdHash);
      const git = simpleGit(path.dirname(gitPath));
      this.agentTraceLogger.debug(`Using shadow git at: ${gitPath}`);
      await git.reset(['--hard', this.cleanCommitHash(commitHash)]); // Hard reset to target commit
      this.agentTraceLogger.debug(`Successfully reset to checkpoint: ${commitHash}`);
    } catch (firstError) {
      // 第一种方法失败，记录日志但不抛出错误
      this.agentTraceLogger.debug(
        `First reset attempt failed, trying alternative method: ${
          firstError instanceof Error ? firstError.message : String(firstError)
        }`
      );
      try {
        const gitPath = await getShadowGitPathWithSessionId(this.globalStoragePath, this.sessionId, this.cwdHash);
        const git = simpleGit(path.dirname(gitPath));
        this.agentTraceLogger.debug(`Using shadow getShadowGitPathWithSessionId at: ${gitPath}`);
        await git.reset(['--hard', this.cleanCommitHash(commitHash)]); // Hard reset to target commit
      } catch (secondError) {
        this.agentTraceLogger.debug(
          `resetHead: Failed to reset to checkpoint ${commitHash}: ${
            secondError instanceof Error ? secondError.message : String(secondError)
          }`
        );
        throw new Error(
          `Failed to reset to checkpoint ${commitHash}: ${
            secondError instanceof Error ? secondError.message : String(secondError)
          }`
        );
      }
    }
  }
  /**
   * Return an array describing changed files between one commit and either:
   *   - another commit, or
   *   - the current working directory (including uncommitted changes).
   *
   * If `rhsHash` is omitted, compares `lhsHash` to the working directory.
   * If you want truly untracked files to appear, `git add` them first.
   *
   * @param lhsHash - The commit to compare from (older commit)
   * @param rhsHash - The commit to compare to (newer commit).
   *                  If omitted, we compare to the working directory.
   * @returns Array of file changes with before/after content
   */
  public async getDiffSet(
    lhsHash: string,
    rhsHash?: string
  ): Promise<
    Array<{
      relativePath: string;
      absolutePath: string;
      before: string;
      after: string;
    }>
  > {
    const gitPath = await getShadowGitPath(this.globalStoragePath, this.sessionId, this.cwdHash);
    const git = simpleGit(path.dirname(gitPath));

    // console.info(`Getting diff between commits: ${lhsHash || 'initial'} -> ${rhsHash || 'working directory'}`);

    // Stage all changes so that untracked files appear in diff summary
    await this.gitOperations.addCheckpointFiles(git);

    const cleanRhs = rhsHash ? this.cleanCommitHash(rhsHash) : undefined;
    const diffRange = cleanRhs ? `${this.cleanCommitHash(lhsHash)}..${cleanRhs}` : this.cleanCommitHash(lhsHash);

    const diffSummary = await git.diffSummary([diffRange]);

    const result = [];
    for (const file of diffSummary.files) {
      const filePath = file.file;
      const absolutePath = path.join(this.cwd, filePath);

      let beforeContent = '';
      try {
        beforeContent = await git.show([`${this.cleanCommitHash(lhsHash)}:${filePath}`]);
      } catch (_) {
        // file didn't exist in older commit => remains empty
      }

      let afterContent = '';
      if (rhsHash) {
        try {
          afterContent = await git.show([`${this.cleanCommitHash(rhsHash)}:${filePath}`]);
        } catch (_) {
          // file didn't exist in newer commit => remains empty
        }
      } else {
        try {
          afterContent = await fs.readFile(absolutePath, 'utf8');
        } catch (_) {
          // file might be deleted => remains empty
        }
      }

      result.push({
        relativePath: filePath,
        absolutePath,
        before: beforeContent,
        after: afterContent
      });
    }

    return result;
  }

  /**
   * Returns the number of files changed between two commits.
   *
   * @param lhsHash - The commit to compare from (older commit)
   * @param rhsHash - The commit to compare to (newer commit).
   *                  If omitted, we compare to the working directory.
   * @returns The number of files changed between the commits
   */
  public async getDiffCount(lhsHash: string, rhsHash?: string): Promise<number> {
    const gitPath = await getShadowGitPath(this.globalStoragePath, this.sessionId, this.cwdHash);
    const git = simpleGit(path.dirname(gitPath));

    // Stage all changes so that untracked files appear in diff summary
    await this.gitOperations.addCheckpointFiles(git);

    const cleanRhs = rhsHash ? this.cleanCommitHash(rhsHash) : undefined;
    const diffRange = cleanRhs ? `${this.cleanCommitHash(lhsHash)}..${cleanRhs}` : this.cleanCommitHash(lhsHash);
    const diffSummary = await git.diffSummary([diffRange]);

    return diffSummary.files.length;
  }
}

export default CheckpointTracker;
