import {
  AssistantMessageContent,
  TextContent,
  Tool<PERSON>se,
  ToolParamName,
  toolParamNames,
  toolUseNames,
  ToolUseName
} from '../types/message';

export function parseAssistantMessage(assistantMessage: string) {
  let contentBlocks: AssistantMessageContent[] = [];
  let currentTextContent: TextContent | undefined = undefined;
  let currentTextContentStartIndex = 0;
  let currentToolUse: ToolUse | undefined = undefined;
  let currentToolUseStartIndex = 0;
  let currentParamName: ToolParamName | undefined = undefined;
  let currentParamValueStartIndex = 0;
  let accumulator = '';
  let inThinkingBlock = false; // 添加标记是否在thinking块内

  // 添加过滤thinking标签的函数
  function filterThinkingContent(text: string): string | null {
    if (!text || text.indexOf('<thinking>') === -1) {
      return text;
    }

    let result = '';
    let currentPosition = 0;

    // 寻找并移除所有thinking块
    while (currentPosition < text.length) {
      const thinkingStart = text.indexOf('<thinking>', currentPosition);

      if (thinkingStart === -1) {
        // 没有更多thinking块，添加剩余文本
        result += text.substring(currentPosition);
        break;
      }

      // 添加thinking开始标签之前的内容
      if (thinkingStart > currentPosition) {
        result += text.substring(currentPosition, thinkingStart);
      }

      // 寻找此thinking块的结束位置
      const thinkingEnd = text.indexOf('</thinking>', thinkingStart);

      if (thinkingEnd === -1) {
        // 未闭合的thinking块，不添加剩余内容
        break;
      }

      // 移动位置到thinking块之后
      currentPosition = thinkingEnd + '</thinking>'.length;
    }

    // 如果过滤后没有剩余内容，返回null
    return result.trim().length > 0 ? result.trim() : null;
  }

  for (let i = 0; i < assistantMessage.length; i++) {
    const char = assistantMessage[i];
    accumulator += char;

    // 检查是否进入或离开thinking块
    if (accumulator.endsWith('<thinking>')) {
      inThinkingBlock = true;
      // 如果之前有文本内容，先保存它
      if (currentTextContent) {
        const content = accumulator.slice(currentTextContentStartIndex, i - '<thinking>'.length).trim();
        if (content) {
          currentTextContent.content = content;
          currentTextContent.partial = false;
          contentBlocks.push(currentTextContent);
        }
        currentTextContent = undefined;
      }
      continue;
    }
    if (accumulator.endsWith('</thinking>')) {
      inThinkingBlock = false;
      currentTextContentStartIndex = i + 1;
      continue;
    }

    // 如果在thinking块内，跳过处理
    if (inThinkingBlock) {
      continue;
    }

    // there should not be a param without a tool use
    if (currentToolUse && currentParamName) {
      const currentParamValue = accumulator.slice(currentParamValueStartIndex);
      const paramClosingTag = `</${currentParamName}>`;
      if (currentParamValue.endsWith(paramClosingTag)) {
        // end of param value
        currentToolUse.params[currentParamName] = currentParamValue.slice(0, -paramClosingTag.length).trim();
        currentParamName = undefined;
        continue;
      } else {
        // partial param value is accumulating
        continue;
      }
    }

    // no currentParamName

    if (currentToolUse) {
      const currentToolValue = accumulator.slice(currentToolUseStartIndex);
      const toolUseClosingTag = `</${currentToolUse.name}>`;
      if (currentToolValue.endsWith(toolUseClosingTag)) {
        // end of a tool use
        currentToolUse.partial = false;
        contentBlocks.push(currentToolUse);
        currentToolUse = undefined;
        continue;
      } else {
        const possibleParamOpeningTags = toolParamNames.map((name) => `<${name}>`);
        for (const paramOpeningTag of possibleParamOpeningTags) {
          if (accumulator.endsWith(paramOpeningTag)) {
            // start of a new parameter
            currentParamName = paramOpeningTag.slice(1, -1) as ToolParamName;
            currentParamValueStartIndex = accumulator.length;
            break;
          }
        }

        // there's no current param, and not starting a new param

        // special case for write_to_file where file contents could contain the closing tag, in which case the param would have closed and we end up with the rest of the file contents here. To work around this, we get the string between the starting content tag and the LAST content tag.
        const contentParamName: ToolParamName = 'content';
        if (currentToolUse.name === 'edit_file' && accumulator.endsWith(`</${contentParamName}>`)) {
          const toolContent = accumulator.slice(currentToolUseStartIndex);
          const contentStartTag = `<${contentParamName}>`;
          const contentEndTag = `</${contentParamName}>`;
          const contentStartIndex = toolContent.indexOf(contentStartTag) + contentStartTag.length;
          const contentEndIndex = toolContent.lastIndexOf(contentEndTag);
          if (contentStartIndex !== -1 && contentEndIndex !== -1 && contentEndIndex > contentStartIndex) {
            currentToolUse.params[contentParamName] = toolContent.slice(contentStartIndex, contentEndIndex).trim();
          }
        }

        // partial tool value is accumulating
        continue;
      }
    }

    // no currentToolUse

    let didStartToolUse = false;
    const possibleToolUseOpeningTags = toolUseNames.map((name) => `<${name}>`);
    for (const toolUseOpeningTag of possibleToolUseOpeningTags) {
      if (accumulator.endsWith(toolUseOpeningTag)) {
        // start of a new tool use
        currentToolUse = {
          type: 'tool_use',
          name: toolUseOpeningTag.slice(1, -1) as ToolUseName,
          params: {},
          partial: true
        };
        currentToolUseStartIndex = accumulator.length;
        // this also indicates the end of the current text content
        if (currentTextContent) {
          currentTextContent.partial = false;
          // remove the partially accumulated tool use tag from the end of text (<tool)
          let content = currentTextContent.content.slice(0, -toolUseOpeningTag.slice(0, -1).length).trim();

          // 过滤thinking标签及内容
          const filteredContent = filterThinkingContent(content);
          if (filteredContent) {
            currentTextContent.content = filteredContent;
            contentBlocks.push(currentTextContent);
          }
          currentTextContent = undefined;
        }

        didStartToolUse = true;
        break;
      }
    }

    if (!didStartToolUse) {
      // no tool use, so it must be text either at the beginning or between tools
      if (currentTextContent === undefined) {
        currentTextContentStartIndex = i;
      }

      // 获取当前文本并过滤thinking内容
      const currentText = accumulator.slice(currentTextContentStartIndex).trim();
      const filteredText = filterThinkingContent(currentText);

      // 只有在过滤后还有内容时才创建TextContent
      if (filteredText) {
        currentTextContent = {
          type: 'text',
          content: filteredText,
          partial: true
        };
      } else {
        // 如果过滤后没有内容，仍然创建一个TextContent但使用空字符串
        // currentTextContent = {
        //   type: "text",
        //   content: "",
        //   partial: true,
        // };
        currentTextContent = undefined;
      }
    }
  }

  if (currentToolUse) {
    // stream did not complete tool call, add it as partial
    if (currentParamName) {
      // tool call has a parameter that was not completed
      currentToolUse.params[currentParamName] = accumulator.slice(currentParamValueStartIndex).trim();
    }
    contentBlocks.push(currentToolUse);
  }

  // Note: it doesnt matter if check for currentToolUse or currentTextContent, only one of them will be defined since only one can be partial at a time
  if (currentTextContent) {
    // 过滤最终的文本内容中的thinking标签
    const filteredContent = filterThinkingContent(currentTextContent.content);
    if (filteredContent) {
      currentTextContent.content = filteredContent;
      // stream did not complete text content, add it as partial
      contentBlocks.push(currentTextContent);
    }
  }

  return contentBlocks;
}
