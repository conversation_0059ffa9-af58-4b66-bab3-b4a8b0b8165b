// Core Node.js imports
import path from 'path';
import fs from 'fs/promises';
import delay from 'delay';

// Internal imports
import { AgentManager } from '../agent';
import { SayTool } from '../types/type';
import { HandleError, PushToolResult, RemoveClosingTag, ToolUse, ReportToolAction } from '../types/message';
import { getReadablePath } from '@/util/path';
import { fileExistsAtPath } from '@/util/fs';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { constructNewFileContent } from '../utils/diff';
import { ReportCodeGenerate } from '../types/tool';

/**
 * Performs search and replace operations on a file
 * @param agent - Cline instance
 * @param block - Tool use parameters
 * @param handleError - Function to handle errors
 * @param pushToolResult - Function to push tool results
 * @param removeClosingTag - Function to remove closing tags
 */
export async function replaceInFileTool(
  agent: Agent<PERSON>anager,
  block: ToolUse,
  handleError: HandleError,
  pushToolResult: PushToolResult,
  removeClosingTag: RemoveClosingTag,
  reportToolAction: ReportToolAction
): Promise<void> {
  // Extract and validate parameters
  const relPath: string | undefined = block.params.path;
  let diff: string | undefined = block.params.diff; // for replace_in_file

  try {
    const sharedMessageProps: SayTool = {
      tool: 'editFile',
      path: getReadablePath(agent.cwd, relPath),
      content: '',
      tool_version: 'v2'
    };
    // Handle partial tool use
    if (block.partial) {
      agent.removeLastPartialMessageIfExistsWithType('ask', 'tool');
      await agent.say('tool', JSON.stringify(sharedMessageProps), block.partial).catch(() => {});
      return;
    }

    // Validate required parameters
   if (!relPath) {
     agent.consecutiveMistakeCount++;
     pushToolResult(await agent.sayAndCreateMissingParamError('replace_in_file', 'path'));
     return;
   }

   // Validate replacements array if provided
   if (!diff) {
     agent.consecutiveMistakeCount++;
     pushToolResult(await agent.sayAndCreateMissingParamError('replace_in_file', 'diff'));
     return;
   }


    // At this point we know relPath is defined
    const validRelPath = relPath as string;

    agent.logger.reportUserAction({
      key: 'agent_tools_request',
      type: 'replace_in_file'
    });
    const startToolTime = Date.now();

    const absolutePath = path.resolve(agent.cwd, validRelPath);
    const fileExists = await fileExistsAtPath(absolutePath);

    if (!fileExists) {
      agent.consecutiveMistakeCount++;
      const formattedError = `File does not exist at path: ${absolutePath}\nThe specified file could not be found. Please verify the file path and try again.`;
      await agent.say('error', formattedError);
      pushToolResult(formattedError);
      return;
    }

    // Reset consecutive mistakes since all validations passed
    agent.consecutiveMistakeCount = 0;

    // Read and process file content
    let fileContent: string;
    try {
      fileContent = await fs.readFile(absolutePath, 'utf-8');
    } catch (error) {
      agent.consecutiveMistakeCount++;
      const errorMessage = `Error reading file: ${absolutePath}\nFailed to read the file content: ${
        error instanceof Error ? error.message : String(error)
      }\nPlease verify file permissions and try again.`;
      await agent.say('error', errorMessage);
      pushToolResult(errorMessage);
      return;
    }
    // try {
    const result = await constructNewFileContent(diff, fileContent || '', !block.partial);
    const newContent = result.content;
    const replacements = result.replacements;
    // } catch (error) {
    //   await agent.say('error', relPath);

    //   // pushToolResult(
    //   //   formatResponse.toolError(
    //   //     `${(error as Error)?.message}\n\n` + formatResponse.diffError(relPath, this.diffViewProvider.originalContent)
    //   //   )
    //   // );
    //   return;
    // }
    agent.reportGenerateCode(replacements.map((replacement) => ({ ...replacement, filePath: validRelPath })));

    agent.removeLastPartialMessageIfExistsWithType('ask', 'tool');
    await agent.say('tool', JSON.stringify({ ...sharedMessageProps, content: newContent }), block.partial);
    await delay(1000); // wait for diff view to update
    let generationCall = agent.trace?.generation({
      name: 'tool_call',
      input: {
        path: validRelPath,
        diff,
        newContent,
        replacements
      },
      metadata: {
        name: block.name
      }
    });

    const { data } = await agent.messenger.request('assistant/agent/writeToFile', {
      path: validRelPath,
      content: newContent,
      newFile: false
    });
    reportToolAction(Date.now() - startToolTime, {
      contentLength: newContent.length,
      noModified: !!data?.noModified,
      lines: fileContent.split('\n').length,
      type: data?.type
    });

    if (data?.noModified) {
      pushToolResult(`No changes needed for '${relPath}'`);
      return;
    }
    if (data?.type === 'success') {
      const resultMessage = [
        `The updated content has been successfully saved to ${validRelPath.toPosix()}.\n\n`,
        `Please note:\n`,
        `1. You do not need to re-write the file with these changes, as they have already been applied.\n`,
        `2. Proceed with the task using the updated file content as the new baseline.\n`,
        `3. If the user's edits have addressed part of the task or changed the requirements, adjust your approach accordingly.`
      ].join('');
      pushToolResult(resultMessage);
    } else {
      await handleError('replace in file', new Error(data?.content || ''), 'replace_in_file');
    }
    generationCall?.end({
      output: { type: data?.type, content: data?.content }
    });
    agent.logger.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-chat-tool',
      millis: Date.now() - startToolTime,
      extra4: data?.type === 'success' ? 'success' : 'error',
      extra6: block.name
    });

    await agent.saveCheckpoint();
  } catch (error: any) {
    await handleError('replace in file', error, 'replace_in_file');
  }
}
