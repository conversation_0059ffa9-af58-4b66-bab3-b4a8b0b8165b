import { MessageParam, MessageParamVersion1 } from "../types/type";

export function isAllMessagesVersion1(messages: MessageParam[]): messages is MessageParamVersion1[] {
  const messagesVersion1: MessageParamVersion1[] = [];
  messages.forEach((m) => {
    if (m.version === 1) {
      messagesVersion1.push(m);
    }
  });

  return messagesVersion1.length === messages.length;
}

export function removeThinkingText(content: string): string {
  return content.replace(/<thinking>[\s\S]*?<\/thinking>\s*/g, '');
}