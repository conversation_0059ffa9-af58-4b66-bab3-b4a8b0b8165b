import { ContextState, TokenCalculator, ContextResult } from '../types/context';
import {
  MessageParamVersion1,
  TextBlockParamToolSection,
  TextBlockParamVersion0,
  TextBlockParamVersion1
} from '../types/type.d';
import { Logger } from '@/util/log';
import { removeThinkingText } from '../utils/message';
import { LangfuseTraceClient } from 'langfuse';
import { ASSISTANT_NAMESPACE } from '@/util/const';

type MessageItem = MessageParamVersion1;
type TextBlockItem = TextBlockParamVersion1;
// 给回复内容留点buffer
const RESPONSE_TOKEN_BUFFER = 4000;

export class ContextManager {
  private state: ContextState;
  private tokenCalculator: TokenCalculator;
  private logger = new Logger('context-manager');
  private trace?: LangfuseTraceClient | null;

  constructor(maxTokens: number, systemPrompt: string, tokenCalculator: TokenCalculator) {
    this.state = {
      maxTokens: maxTokens - RESPONSE_TOKEN_BUFFER,
      systemPrompt
    };
    this.tokenCalculator = tokenCalculator;
  }

  /**
   * 根据chatId对消息进行分组
   *
   * 【功能说明】
   * 将消息列表按照chatId进行分组，每个chatId对应一组消息。
   * 这样可以分别处理不同对话的上下文，提高对话管理的灵活性。
   *
   * 【处理流程】
   * 1. 遍历所有消息
   * 2. 根据消息的chatId属性进行分组
   * 3. 返回分组后的二维消息数组，每个子数组包含同一chatId的消息
   *
   * 【可视化示例】
   *  输入消息列表:
   *  ┌───────────────────────────────────────────────────────────────────────┐
   *  │ 消息1(chatId=A), 消息2(chatId=A), 消息3(无chatId), 消息4(chatId=B), 消息5(chatId=A) │
   *  └───────────────────────────────────────────────────────────────────────┘
   *                                    │
   *                                    ▼
   *                              【分组处理】
   *                                    │
   *                                    ▼
   *  ┌───────────────────┐    ┌───────────────┐    ┌───────────────┐
   *  │   chatId=A 组     │    │  无chatId 组   │    │  chatId=B 组  │
   *  ├───────────────────┤    ├───────────────┤    ├───────────────┤
   *  │ 消息1             │    │ 消息3         │    │ 消息4         │
   *  │ 消息2             │    │               │    │               │
   *  │ 消息5             │    │               │    │               │
   *  └───────────────────┘    └───────────────┘    └───────────────┘
   *
   * 【应用场景】
   * - 多对话并行处理时，需要区分不同对话的上下文
   * - 对话历史压缩时，需要按对话分别处理
   * - 分析特定对话的交互模式时
   *
   * @param messages 需要分组的消息列表
   * @returns 按chatId分组后的消息二维数组
   */
  groupAgentMessages(messages: MessageItem[]): MessageItem[][] {
    // 使用数组存储分组结果，保持严格顺序
    const groupedArray: MessageItem[][] = [];
    // 用于快速查找已有分组的索引
    const chatIdToGroupIndex = new Map<string, MessageItem[]>();

    // 遍历所有消息进行分组
    for (const message of messages) {
      // 如果消息没有chatId，则为其创建一个独立分组
      if (!message.chatId) {
        groupedArray.push([message]);
        continue;
      }

      const chatId = message.chatId;

      // 检查这个chatId是否已经有对应的分组
      const existingGroup = chatIdToGroupIndex.get(chatId);
      if (existingGroup) {
        // 添加到已有分组
        existingGroup.push(message);
      } else {
        // 创建新分组
        const newGroup = [message];
        groupedArray.push(newGroup);
        chatIdToGroupIndex.set(chatId, newGroup);
      }
    }

    return groupedArray;
  }

  flattenAgentMessages(messages: MessageItem[][]): MessageItem[] {
    return messages.flat();
  }

  compressOneAgentHistories(messages: MessageItem[]): MessageItem[] {
    if (messages.length === 0) {
      return [];
    }

    const chatId = messages[0].chatId;
    const version = messages[0].version || 0;
    const assistantContent = messages
      .filter((m) => m.role === 'assistant')
      .map((m) => {
        const contentList = Array.isArray(m.content) ? m.content.map((c) => c.text) : [m.content];
        // 过滤thinking标签及其内容
        const content = contentList.map((c) => removeThinkingText(c));
        return content.join('\n');
      })
      .join('\n');

    const firstUserMessage = messages.find((m) => m.role === 'user');
    // 如果没有找到用户消息，则返回空
    if (!firstUserMessage) {
      this.logger.error('No user message found in the provided messages', { messages });
      return [];
    }

    const generateQAPair = (userContent: string): MessageItem[] => {
      const finalMessages: MessageItem[] = [
        {
          content: userContent,
          role: 'user',
          chatId: chatId,
          version
        }
      ];

      if (assistantContent) {
        finalMessages.push({
          content: assistantContent,
          role: 'assistant',
          chatId: chatId,
          version
        });
      }
      return finalMessages;
    };

    if (typeof firstUserMessage.content === 'string') {
      return generateQAPair(firstUserMessage.content);
    }

    // NOTE: 如果agent消息组中，第一条user消息，没有有效的user-input，则整个组会被过滤
    const userInputContent = firstUserMessage.content.find((c) => c.category === 'user-input');
    if (!userInputContent) {
      this.logger.error('No user input content found in the provided messages', { messages });
      return [];
    }

    return generateQAPair(userInputContent.text);
  }

  /**环境信息压缩器 - 仅保留最新用户消息中的环境信息
   *
   * 【功能说明】
   * 在长对话中，环境信息(environments)会重复出现在多个历史消息中。
   * 为优化token使用，仅需保留最新用户消息中的环境信息，历史消息中的可以安全删除。
   *
   * 【处理流程】
   * 1. 反向遍历消息列表
   * 2. 找到第一个用户消息(保留其环境信息)
   * 3. 继续遍历剩余历史消息，删除所有环境信息
   *
   * 【边界情况处理】
   * - 空消息列表: 直接返回空数组
   * - 没有用户消息: 保持消息不变
   * - 消息内容格式:
   *   - 字符串内容: 直接跳过
   *   - 数组内容: 过滤environment-details类型
   * - 版本兼容: 保持消息的version属性
   * - chatId维护: 保持消息的chatId属性
   *
   * 【示例】
   * 输入:
   * [
   *   {role: 'user', content: [{category: 'environment-details'}, {category: 'user-input'}]}, // 最新
   *   {role: 'assistant', content: 'response'},
   *   {role: 'user', content: [{category: 'environment-details'}, {category: 'user-input'}]}, // 历史
   * ]
   *
   * 输出:
   * [
   *   {role: 'user', content: [{category: 'environment-details'}, {category: 'user-input'}]}, // 保留
   *   {role: 'assistant', content: 'response'},
   *   {role: 'user', content: [{category: 'user-input'}]}, // 环境信息被删除
   * ]
   *
   * @param messages 需要处理的消息列表
   * @returns 处理后的消息列表
   */
  compressFormerEnvironmentsInfo(messages: MessageItem[]): MessageItem[] {
    // 处理边界情况：空消息列表直接返回
    if (messages.length === 0) {
      return [];
    }

    // 创建结果数组，用于存储处理后的消息
    const result: MessageItem[] = [];

    // 标记是否已找到最新的用户消息
    let foundLatestUserMessage = false;

    // 反向遍历消息列表，从最新消息开始
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];

      // 如果是用户消息且内容是数组
      if (message.role === 'user' && Array.isArray(message.content)) {
        if (!foundLatestUserMessage) {
          // 这是最新的用户消息，保留其环境信息
          result.unshift(message);
          foundLatestUserMessage = true;
        } else {
          // 这是历史用户消息，需要删除环境信息
          const filteredContent = message.content.filter((item) => item.category !== 'environment-details');

          // 创建新的消息对象，保留原始消息的其他属性
          result.unshift({
            ...message,
            content: filteredContent
          });
        }
      } else {
        // 非用户消息或内容不是数组的消息，直接保留
        result.unshift(message);
      }
    }

    this.logger.info(
      `Compressed environments info in ${messages.length} messages, found latest user message: ${foundLatestUserMessage}`,
      { messages, result }
    );

    return result;
  }

  /** 对话历史压缩器 - 将多轮Agent-LLM对话压缩为更紧凑的格式
   *
   * 【功能说明】
   * 当用户与AI Agent交互时，Agent会与LLM进行多轮对话。随着对话累积，
   * 历史记录会变得冗长，占用大量token。本方法通过智能压缩历史对话，
   * 在保留关键上下文的同时减少token使用量。
   *
   * 【处理流程】
   * 假设对话历史为: Q(当前问题), Q1, A1, Q2, A2, Q3, A3...
   * 其中:
   *   - Q(当前问题), Q1, A1: 当前进行中的对话
   *   - Q2, A2, Q3, A3...: 历史对话轮次
   *
   * 本方法将:
   *   1. 识别并分组历史对话轮次
   *   2. 将每组对话压缩为单个Q', A'对
   *   3. 返回压缩后的历史: Q(当前问题), Q1, A1, Q', A'
   *
   * 【可视化示例】
   *                  ┌───────────────────────────────────────────┐
   *                  │            原始对话历史结构                  │
   *                  └───────────────────────────────────────────┘
   *                                     │
   *                                     ▼
   *  ┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
   *  │    当前对话      │      │   上一轮对话      │      │   更早的对话      │
   *  ├─────────────────┤      ├─────────────────┤      ├─────────────────┤
   *  │ Q(当前问题)      │      │ Q2              │      │ Q4              │
   *  │ Q1              │      │ A2              │      │ A4              │
   *  │ A1              │      │ Q3              │      │ A5              │
   *  │                 │      │ A3              │      │                 │
   *  └─────────────────┘      └─────────────────┘      └─────────────────┘
   *          │                        │                        │
   *          │                        │                        │
   *          │                        ▼                        ▼
   *          │                   【压缩处理】              【压缩处理】
   *          │                        │                        │
   *          │                        │                        │
   *          ▼                        ▼                        ▼
   *  ┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
   *  │    当前对话      │      │  压缩的上一轮     │      │ 压缩的更早对话     │
   *  ├─────────────────┤      ├─────────────────┤      ├─────────────────┤
   *  │ Q(当前问题)      │      │ Q'              │      │ Q''             │
   *  │ Q1              │      │ A'              │      │ A''             │
   *  │ A1              │      │                 │      │                 │
   *  └─────────────────┘      └─────────────────┘      └─────────────────┘
   *          │                        │                        │
   *          └────────────────────────┴────────────────────────┘
   *                                   │
   *                                   ▼
   *                  ┌───────────────────────────────────────┐
   *                  │           压缩后的对话历史               │
   *                  │  Q(当前问题), Q1', A1', Q2, A2...      │
   *                  └───────────────────────────────────────┘
   *
   * 【优势】
   * - 显著减少token使用量
   * - 保留关键上下文信息
   * - 提高LLM响应速度和质量
   * - 优化长对话场景下的性能
   */
  compressFormerFullAgentQAHistories(messages: MessageItem[]): MessageItem[] {
    const groupedAgentMessages = this.groupAgentMessages(messages);

    // 如果没有分组，直接返回空
    if (groupedAgentMessages.length === 0) {
      return [];
    }

    // 压缩除最后一组外的所有组
    const compressedGroups = groupedAgentMessages.map((group, index) => {
      // 如果是最后一组，不压缩
      if (index === groupedAgentMessages.length - 1) {
        return group;
      }
      // 压缩其他组
      return this.compressOneAgentHistories(group);
    });

    const flattenedMessages = this.flattenAgentMessages(compressedGroups);
    this.logger.info(
      `Compressed compressFormerFullAgentQAHistories, ${groupedAgentMessages.length} groups of messages, last group preserved`,
      { compressedGroups, messages }
    );
    return flattenedMessages;
  }

  /** 最近对话压缩器 - 仅压缩最近一次Agent对话中的LLM交互历史
   *
   * 【功能说明】
   * 与compressFullFormerAgentQAHistories不同，本方法只关注最近一次Agent对话中的LLM交互，
   * 不处理更早的历史对话。这种精确压缩适用于保留最新上下文的同时优化token使用。
   *
   * 【处理流程】
   * 假设对话历史为: Q(当前问题), Q1, A1, Q2, A2, Q3, A3...
   * 其中:
   *   - Q(当前问题): 用户当前的问题
   *   - Q1, A1: 最近一次Agent与LLM的对话
   *   - Q2, A2, Q3, A3...: 更早的历史对话
   *
   * 本方法将:
   *   1. 只压缩Q1, A1部分
   *   2. 将Q1, A1压缩为Q1', A1'
   *   3. 保持其他部分不变
   *   4. 返回压缩后的历史: Q(当前问题), Q1', A1', Q2, A2, Q3, A3...
   *
   * 【可视化示例】
   *                  ┌───────────────────────────────────────────┐
   *                  │            原始对话历史结构                  │
   *                  └───────────────────────────────────────────┘
   *                                     │
   *                                     ▼
   *  ┌─────────────────────────────────────────────┐      ┌─────────────────┐
   *  │          当前Agent对话                       │      │   历史对话        │
   *  ├─────────────────┬───────────────────────────┤      ├─────────────────┤
   *  │ 当前用户问题      │  最近LLM交互               │      │ Q2              │
   *  ├─────────────────┼───────────────────────────┤      │ A2              │
   *  │ Q(当前问题)      │  Q1                       │      │ Q3              │
   *  │                 │  A1                       │      │ A3              │
   *  └─────────────────┴───────────────────────────┘      └─────────────────┘
   *                          │                                   │
   *                          │                                   │
   *                          ▼                                   │
   *                     【压缩处理】                               │
   *                          │                                   │
   *                          │                                   │
   *                          ▼                                   ▼
   *  ┌─────────────────────────────────────────────┐      ┌─────────────────┐
   *  │          当前Agent对话                       │      │   历史对话        │
   *  ├─────────────────┬───────────────────────────┤      ├─────────────────┤
   *  │ 当前用户问题      │  压缩后的LLM交互            │      │ Q2              │
   *  ├─────────────────┼───────────────────────────┤      │ A2              │
   *  │ Q(当前问题)      │  Q1'                      │      │ Q3              │
   *  │                 │  A1'                      │      │ A3              │
   *  └─────────────────┴───────────────────────────┘      └─────────────────┘
   *                                │                              │
   *                                └──────────────────────────────┘
   *                                             │
   *                                             ▼
   *                  ┌───────────────────────────────────────┐
   *                  │           压缩后的对话历史               │
   *                  │  Q(当前问题), Q1', A1', Q2, A2...      │
   *                  └───────────────────────────────────────┘
   *
   * 【应用场景】
   * - 当最近一次Agent-LLM对话包含大量细节但可被简化时
   * - 需要保留完整历史上下文但优化最新交互时
   * - 当用户问题需要最近对话的精简版本而非完整细节时
   */
  compressLLMHistoriesInLatestAgentQA(messages: MessageItem[]): MessageItem[] {
    const groupedMessages = this.groupAgentMessages(messages);
    // 获取最新的一组对话
    const latestGroup = groupedMessages.pop();
    // 如果没有分组，直接返回空
    if (!latestGroup) {
      return [];
    }

    const filteredLatestMessages: MessageItem[] = [];

    groupedMessages.forEach((group) => {
      filteredLatestMessages.push(...group);
    });

    // 找出最新组中最后一个用户消息的索引
    let lastUserMessageIndex = -1;
    for (let i = latestGroup.length - 1; i >= 0; i--) {
      if (latestGroup[i].role === 'user') {
        lastUserMessageIndex = i;
        break;
      }
    }

    // 处理最新组中的每个消息
    latestGroup.forEach((m, index) => {
      // 如果消息不是用户消息，或者是字符串内容，或者是最后一个用户消息，直接保留
      if (
        m.role !== 'user' ||
        typeof m.content === 'string' ||
        index === lastUserMessageIndex // 最后一个用户消息不压缩
      ) {
        filteredLatestMessages.push(m);
        return;
      }

      // 对于其他用户消息，精简工具结果
      m.content = m.content.map((c) => {
        if (c.category === 'tool-response') {
          return {
            ...c,
            // 省略工具结果
            text: '(tool response omitted for brevity)'
          };
        }
        return c;
      });

      filteredLatestMessages.push(m);
    });

    this.logger.info(
      `Compressing latest group of ${messages.length} messages with compressLLMHistoriesInLatestAgentQA, preserving last user message`,
      { latestGroup, filteredLatestMessages, lastUserMessageIndex }
    );
    return filteredLatestMessages;
  }

  /** 删除Assistant消息中的thinking内容
   *
   * 【功能说明】
   * 删除assistant消息中所有<thinking></thinking>标签及其包含的内容,
   * 保留其他内容的完整性。
   *
   * 【处理流程】
   * 1. 遍历消息列表
   * 2. 对assistant消息进行thinking标签过滤:
   *    - 字符串类型: 直接过滤thinking标签
   *    - 数组类型: 对每个元素过滤thinking标签
   * 3. 保持非assistant消息不变
   *
   * 【边界情况处理】
   * 1. 空消息列表: 返回空数组
   * 2. 不完整标签:
   *    - 只有开始标签: 保留标签后内容
   *    - 只有结束标签: 保留标签前内容
   * 3. 嵌套标签: 从内到外逐层处理
   * 4. 多个thinking块: 逐个处理并拼接剩余内容
   * 5. 空thinking内容: 直接移除标签
   * 6. 标签前后空格: 智能保留必要空格
   *
   * @param messages 需要处理的消息列表
   * @returns 处理后的消息列表
   */
  compressLLMHistoriesThinking(messages: MessageItem[]): MessageItem[] {
    // 处理边界情况：空消息列表直接返回
    if (messages.length === 0) {
      return [];
    }

    // 创建结果数组，用于存储处理后的消息
    const result: MessageItem[] = [];

    // 遍历所有消息
    for (const message of messages) {
      // 如果不是assistant消息，直接添加到结果中
      if (message.role !== 'assistant') {
        result.push(message);
        continue;
      }

      // 处理assistant消息
      if (typeof message.content === 'string') {
        // 字符串类型：直接过滤thinking标签及其内容
        const filteredContent = removeThinkingText(message.content);
        result.push({
          ...message,
          content: filteredContent
        });
      } else if (Array.isArray(message.content)) {
        // 数组类型：对每个元素过滤thinking标签
        const filteredContent = message.content.map((item) => {
          if (item.type === 'text') {
            return {
              ...item,
              text: removeThinkingText(item.text)
            };
          }
          return item;
        });
        result.push({
          ...message,
          content: filteredContent
        });
      } else {
        // 其他情况：直接添加原始消息
        result.push(message);
      }
    }

    this.logger.info(`Compressed thinking content in ${messages.length} messages`, { messages, result });

    return result;
  }

  async optimizeMessagesContext(messages: MessageItem[], trace?: LangfuseTraceClient | null): Promise<ContextResult> {
    const startTime = Date.now();
    try {
      if (!this.state.availableTokens) {
        this.state.availableTokens =
          this.state.maxTokens - (await this.tokenCalculator.calculate(this.state.systemPrompt));
      }
      this.trace = trace;
      let currentMessages = [...messages];

      // [Compress] 过滤历史对话中的thinking标签
      currentMessages = this.compressLLMHistoriesThinking(currentMessages);

      // [Compress] 删减历史 environment details
      currentMessages = this.compressFormerEnvironmentsInfo(currentMessages);

      let currentTokens = await this.calculateBaseTokens(currentMessages);
      this.logger.info(`Initial tokens: ${currentTokens}, max tokens: ${this.state.maxTokens}`);

      // [Compress] 首先尝试压缩agent历史对话
      if (currentTokens > this.state.availableTokens && currentMessages.length > 1) {
        const before = [...currentMessages];
        const beforeTokens = currentTokens;

        const generation = this.trace?.generation({
          name: 'context_compression',
          input: {
            method: 'compressFormerFullAgentQAHistories',
            beforeTokens,
            beforeMessages: before.length
          }
        });
        currentMessages = this.compressFormerFullAgentQAHistories(currentMessages);
        const afterTokens = await this.calculateBaseTokens(currentMessages);
        if (generation) {
          generation.end({ output: { afterTokens, afterMessages: currentMessages.length } });
        }
        currentTokens = afterTokens;
        this.logger.info(`After compressFormerFullAgentQAHistories: ${currentTokens} tokens`);
      }

      // [Compress] 如果压缩后仍然超长，尝试压缩 LLM history
      if (currentTokens > this.state.availableTokens && currentMessages.length > 1) {
        const before = [...currentMessages];
        const beforeTokens = currentTokens;
        const generation = this.trace?.generation({
          name: 'context_compression',
          input: {
            method: 'compressLLMHistories',
            beforeTokens,
            beforeMessages: before.length
          }
        });
        currentMessages = this.compressLLMHistoriesInLatestAgentQA(currentMessages);
        const afterTokens = await this.calculateBaseTokens(currentMessages);
        if (generation) {
          generation.end({ output: { afterTokens, afterMessages: currentMessages.length } });
        }
        currentTokens = afterTokens;
        this.logger.info(`After compressLLMHistoriesInLatestAgentQA: ${currentTokens} tokens`);
      }

      // [Compress] 如果仍然超长，尝试删除agent历史对话
      if (currentTokens > this.state.availableTokens && currentMessages.length > 1) {
        const generation = this.trace?.generation({
          name: 'context_compression',
          input: {
            method: 'truncateAgentHistory',
            beforeTokens: currentTokens,
            beforeMessages: currentMessages.length
          }
        });
        currentMessages = await this.truncateAgentHistory(currentMessages, currentTokens);
        const afterTokens = await this.calculateBaseTokens(currentMessages);
        if (generation) {
          generation.end({ output: { afterTokens, afterMessages: currentMessages.length } });
        }
        currentTokens = afterTokens;
        this.logger.info(`After truncateAgentHistory: ${currentTokens} tokens`);
      }
      // [Compress] 如果仍然超长，计算历史消息的token数量，确定当前消息可用的token数量
      if (currentTokens > this.state.availableTokens) {
        // 分离历史消息和当前消息
        const historyMessages = currentMessages.slice(0, -1);
        const currentMessage = currentMessages[currentMessages.length - 1];

        // 计算历史消息的token数量
        const historyTokens = historyMessages.length > 0 ? await this.calculateBaseTokens(historyMessages) : 0;

        // 计算当前消息可用的token数量
        const availableTokens = this.state.availableTokens - historyTokens;
        this.logger.info(`History tokens: ${historyTokens}, available tokens for current message: ${availableTokens}`);

        // 处理当前消息，确保它不超过可用token数量
        const before = JSON.stringify(currentMessage);
        const generation = this.trace?.generation({
          name: 'context_compression',
          input: {
            method: 'processMessageWithTokenLimit',
            availableTokens,
            beforeMessagesLength: before.length
          }
        });
        const processedMessage = await this.processMessageWithTokenLimit(currentMessage, availableTokens);
        const after = JSON.stringify(processedMessage);
        if (generation) {
          generation.end({ output: { processedMessage, afterMessagesLength: after.length } });
        }
        // 更新当前消息和总token数
        currentMessages[currentMessages.length - 1] = processedMessage;
        currentTokens = await this.calculateBaseTokens(currentMessages);
        this.logger.info(`After processing current message: ${currentTokens} tokens`);
      }
      this.logger.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'kwaipilot-ide-agent-contextManage',
        millis: Date.now() - startTime,
        extra4: `success`
      });
      return {
        messages: currentMessages
      };
    } catch (error) {
      this.logger.error('Failed to optimize messages context:', error);
      this.logger.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'kwaipilot-ide-agent-contextManage',
        millis: Date.now() - startTime,
        extra4: `error`
      });
      return {
        messages
      };
    }
  }

  // 截断 Agent历史消息，按chatId分组，依次删除前面的分组直到token小于maxToken，且最后一组不能删除
  private async truncateAgentHistory(messages: MessageItem[], tokens: number): Promise<MessageItem[]> {
    if (!messages.length) return [];
    const grouped = this.groupAgentMessages(messages);
    if (grouped.length === 1) return grouped[0];
    // 最后一组不能删除
    let remainGroups = grouped.slice();
    let flattened = this.flattenAgentMessages(remainGroups);
    while (tokens > (this.state.availableTokens || 0) && remainGroups.length > 1) {
      // 删除最前面一组
      remainGroups.shift();
      flattened = this.flattenAgentMessages(remainGroups);
      tokens = await this.calculateBaseTokens(flattened);
    }
    return flattened;
  }

  // 判断是否为 read_file 内容
  private isReadFileContent(content: TextBlockItem): content is TextBlockParamToolSection & TextBlockParamVersion0 {
    return content.category === 'tool-response' && content.toolName === 'read_file';
  }

  // 判断是否为 grep_search 内容
  private isGrepSearchContent(content: TextBlockItem): content is TextBlockParamToolSection & TextBlockParamVersion0 {
    return content.category === 'tool-response' && content.toolName === 'grep_search';
  }

  // 截断 grep_search 结果
  private async truncateSearchResults(
    results: { [key: string]: any[] },
    maxTokens: number,
    sampleTokens: number
  ): Promise<{ [key: string]: any[] }> {
    // 如果结果为空，直接返回
    const resultsEntries = Object.entries(results);
    if (resultsEntries.length === 0) {
      return results;
    }

    // 第一步：快速预估 - 使用更精确的估算方法
    // 先计算一个样本的实际token数，用于校准估算比例
    const sampleEstimated = Math.ceil(JSON.stringify(results).length / 4);
    // 计算校准系数
    const calibrationFactor = sampleTokens / sampleEstimated || 1;

    // 第二步：使用二分查找找到合适的结果数量
    let left = 1;
    let right = resultsEntries.length;
    let finalResults: { [key: string]: any[] } = {};

    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const tempResults: { [key: string]: any[] } = {};

      // 取前 mid 个结果
      for (let i = 0; i < mid; i++) {
        const [path, matches] = resultsEntries[i];
        tempResults[path] = matches;
      }

      // 使用 calibrationFactor 预估 token 数
      const estimatedTokens = Math.ceil((JSON.stringify(tempResults).length / 4) * calibrationFactor);

      if (estimatedTokens <= maxTokens) {
        // 当前数量可行,保存结果并尝试增加
        finalResults = tempResults;
        left = mid + 1;
      } else {
        // 当前数量过大,需要减少
        right = mid - 1;
      }
    }

    // 第三步：验证实际token数
    // 只计算一次实际token数，避免多次API调用
    const actualTokens = await this.tokenCalculator.calculate(JSON.stringify(finalResults));

    // 如果实际token数在限制范围内，直接返回截断后的结果
    if (actualTokens <= maxTokens) {
      return finalResults;
    }

    // 第四步：如果实际token数仍然超出限制，进行精确二分调整
    if (actualTokens > maxTokens) {
      left = 1;
      right = Object.keys(finalResults).length;
      finalResults = {}; // 重置结果

      while (left <= right) {
        const mid = Math.floor((left + right) / 2);
        const tempResults: { [key: string]: any[] } = {};

        // 取前 mid 个结果
        const entries = Object.entries(results).slice(0, mid);
        for (const [path, matches] of entries) {
          tempResults[path] = matches;
        }

        const currentTokens = await this.tokenCalculator.calculate(JSON.stringify(tempResults));

        if (currentTokens <= maxTokens) {
          finalResults = tempResults;
          left = mid + 1;
        } else {
          right = mid - 1;
        }
      }
    }

    // 确保至少保留一个结果
    if (Object.keys(finalResults).length === 0 && resultsEntries.length > 0) {
      const [path, matches] = resultsEntries[0];
      finalResults = { [path]: matches };
    }

    return finalResults;
  }

  /**
   * 根据token限制处理消息
   * 该方法处理单个消息，使其内容不超过指定的token限制
   * 主要处理文件内容和搜索结果等大型内容
   */
  private async processMessageWithTokenLimit(message: MessageItem, maxTokens: number): Promise<MessageItem> {
    // 如果不是用户消息或内容不是数组，直接返回
    if (message.role !== 'user' || !Array.isArray(message.content)) {
      return message;
    }

    const processedContent: TextBlockItem[] = [];
    let currentTokens = 0;

    for (let i = 0; i < message.content.length; i++) {
      const content = message.content[i];

      // 需要尝试处理的 grep_search 内容
      if (this.isGrepSearchContent(content)) {
        const grepResult = content.text;
        try {
          // 解析搜索结果
          const results = JSON.parse(grepResult) as { [key: string]: any[] };

          // 计算搜索结果的token数量
          const resultsTokens = await this.tokenCalculator.calculate(JSON.stringify(results));

          // 如果添加搜索结果会超出限制，则需要截断
          if (currentTokens + resultsTokens > maxTokens) {
            // 截断搜索结果
            const truncatedResults = await this.truncateSearchResults(
              results,
              maxTokens - currentTokens,
              resultsTokens
            );

            // 添加截断后的搜索结果
            processedContent.push({
              type: 'text',
              text: `${JSON.stringify(
                truncatedResults
              )}\n\n[Note: Some search results were truncated due to token limit.]`,
              category: 'tool-response',
              toolName: 'grep_search',
              params: content.params
            });

            // 更新已使用的token数
            currentTokens += await this.tokenCalculator.calculate(JSON.stringify(truncatedResults));
          } else {
            // 如果不超出限制，直接添加
            processedContent.push(content);
            currentTokens += resultsTokens;
          }

          continue;
        } catch (error) {
          // 如果解析失败，直接添加原始内容
          this.logger.error('Failed to parse grep search results:', error);
          processedContent.push(content);
          currentTokens += await this.tokenCalculator.calculate(content.text);
          continue;
        }
      }

      const tokens = await this.tokenCalculator.calculate(
        content.type === 'text' ? content.text : JSON.stringify(content)
      );
      // 如果不超出限制，直接添加
      processedContent.push(content);
      currentTokens += tokens;
    }

    // 返回处理后的消息
    return {
      role: message.role,
      content: processedContent,
      chatId: message.chatId,
      version: message.version
    };
  }

  // 计算消息的 token 数
  private async calculateBaseTokens(messages?: MessageItem[]): Promise<number> {
    if (!messages) {
      return 0;
    }
    return await this.tokenCalculator.calculate(JSON.stringify(messages));
  }
}
