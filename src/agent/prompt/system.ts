import { McpServer } from '../../mcp/types';
import { CLAUDE_SYSTEM_PROMPT } from './claude';
export * from './common';

/**
 * 根据模型类型选择合适的系统提示词
 * @param model 模型名称
 * @param cwd 当前工作目录
 * @param mcpServers MCP服务器列表
 * @param enableRepoIndex 是否启用代码库索引
 * @param rules 用户规则
 * @param shell 终端类型
 * @returns 适用于指定模型的系统提示词
 */
export const SYSTEM_PROMPT = (
  model: string,
  cwd: string,
  mcpServers: McpServer[],
  enableRepoIndex: boolean = false,
  rules?: string[],
  shell: string = '',
  useNewEditTool: boolean = true
) => CLAUDE_SYSTEM_PROMPT({cwd, mcpServers, enableRepoIndex, rules, shell, useNewEditTool});

export { CLAUDE_SYSTEM_PROMPT };
