import { ToolArgs } from './types';

export function getEditAndFileDescription(args: ToolArgs): string {
  return `## edit_file
Use this tool to propose an edit to an existing file or create a new file.
This will be read by a less intelligent model, which will quickly apply the edit. You should make it clear what the edit is, while also minimizing the unchanged code you write.
When writing the edit, you should specify each edit in sequence, with the special comment \`// ... existing code ...\` to represent unchanged code in between edited lines.
For example:
// ... existing code ...
FIRST_EDIT
// ... existing code ...
SECOND_EDIT
// ... existing code ...
THIRD_EDIT
// ... existing code ...
You should still bias towards repeating as few lines of the original file as possible to convey the change.
But, each edit should contain sufficient context of unchanged lines around the code you're editing to resolve ambiguity.
DO NOT omit spans of pre-existing code (or comments) without using the \`// ... existing code ...\` comment to indicate its absence. If you omit the existing code comment, the model may inadvertently delete these lines.
Make sure it is clear what the edit should be, and where it should be applied.
You should specify the following arguments before the others: [target_file]
Parameters:
- target_file: (required) The target file to modify. Use the relative path in the workspace of the file to edit (relative to the current working directory ${args.cwd.toPosix()}).
- instructions: (required) A single sentence instruction describing what you are going to do for the sketched edit. This is used to assist the less intelligent model in applying the edit. Please use the first person to describe what you are going to do. Don't repeat what you have said previously in normal messages. And use it to disambiguate uncertainty in the edit.
- code_edit: (required) Specify ONLY the precise lines of code that you wish to edit. **NEVER specify or write out unchanged code**. Instead, represent all unchanged code using the comment of the language you're editing in - example: \`// ... existing code ...\`
- language: (optional) Specify the programming language of the file to edit. This helps the model apply the edit correctly. If not provided, the model will attempt to infer the language from the file extension. Language list: 'javascript' | 'typescript' | 'python' | 'java' | 'html' | 'css' | 'scss' | 'less' | 'json' | 'jsonc' | 'yaml' | 'markdown' | 'vue' | 'vue-html' | 'xml' | 'php' | 'shellscript' | 'tsx' | 'jsx' | 'sql' | 'wasm' | 'r' | 'ruby' | 'rust' | 'javascriptreact' | 'typescriptreact' | 'c' | 'csharp' | 'plaintext' | 'jade' | 'dockerfile' and more.
Usage:
<edit_file>
<target_file>Relative file path here</target_file>
<instructions>Your instruction here</instructions>
<code_edit>
// ... existing code ...
FIRST_EDIT
// ... existing code ...
SECOND_EDIT
// ... existing code ...
THIRD_EDIT
// ... existing code ...
</code_edit>
<language>Optional language here</language>
</edit_file>
`;
}