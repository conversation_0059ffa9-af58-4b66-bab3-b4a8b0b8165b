import { ContextGraph } from '@/code/analyze/ContextGraph';
import { filename_to_lang } from '@/code/chunk/code';
import { LanceDB } from '@/db/lancedb';
import { CacheStatusTable, CodeInfoTable, FileStatusTable } from '@/db/sqlite/tables';
import { getFileMd5 } from '@/util/file';
import { AGENT_NAMESPACE, generateCollectionName, getCacheCollectionName } from '@/util/const';
import { NodeGraphTable } from '@/db/sqlite/tables/node-graph';
import { PROGRAM_FILES } from '@/code/analyze/code_utils';
import { Logger } from '@/util/log';
import { GlobalConfig } from '@/util/global';
import fs from 'fs';
const logger = new Logger('Indexing');
export type IndexFileAction = 'modify' | 'delete' | 'create';

export const indexFiles = async (files: string[], dirPath: string) => {
  logger.info(`index files ${files}`);

  // Create a new context graph with the repo directory
  const cg = new ContextGraph(dirPath);
  await cg.initContextNodes(files);
  // Build and save the context graph
  await cg.buildContextGraph();
  await cg.saveContextGraph();

  if (cg.total_file_num === 0) {
    throw new Error('index files failed, 索引文件失败');
  }

  // Insert into database
  const collectionName = generateCollectionName(dirPath);
  logger.info(`inserting data to ${collectionName}`);
  const codeData = cg.getContextGraphNodeList();

  logger.info('index files done, 索引文件完成');
};

// 单文件索引
export const indexFile = async (file: string, dirPath: string, action: IndexFileAction) => {
  const collectionName = generateCollectionName(dirPath);
  const startTime = Date.now();
  try {
    logger.info(`start index file ${file} ${action}`);
    if (action === 'delete') {
      await clearIndexByFilePath(file, dirPath);
      logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'index_file_delete',
        millis: Date.now() - startTime,
        extra6: 'success'
      });
      logger.info(`index file ${file} ${action} done, 删除索引完成`);
      return;
    }
    // 判断文件是否存在
    if (!fs.existsSync(file) || fs.statSync(file).isDirectory()) {
      logger.debug(`file ${file} not found or is a directory`);
      return;
    }
    if (action === 'modify') {
      const result = await modifyIndexByFilePath(file, dirPath);
      if (result) {
        logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'index_file_modify_from_cache',
          millis: Date.now() - startTime,
          extra6: 'success'
        });
        logger.info(`index file ${file} ${action} done, 修改索引完成`);
        return;
      }
    }
    // 如果缓存中已经存在数据，则直接返回
    const result = await indexFileFromCache(file, collectionName);
    if (result) {
      logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'index_file_from_cache',
        millis: Date.now() - startTime,
        extra6: 'success'
      });
      logger.info(`index file ${file} ${action} done, 从缓存中获取数据完成`);
      return;
    }
    logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'INDEXING_indexFileFromCache',
      millis: Date.now() - startTime,
      extra4: file,
      extra6: 'success'
    });
    logger.reportUserAction({
      key: 'INDEXING',
      type: 'indexFileFromCache',
      subType: 'success',
      content: JSON.stringify({
        repo_dir: dirPath,
        file: file,
        time: Date.now() - startTime
      })
    });
    let indexStartTime = Date.now();
    const cg = ContextGraph.getContextGraph(dirPath);
    const lang = filename_to_lang(file);
    if (!lang || !PROGRAM_FILES.includes(lang)) {
      await cg.indexNonCodeFile(file);
      logger.info(`index file ${file} ${action} done, 索引非代码文件完成`);
    } else {
      // todo: 索引之前，需要将数据库中之前索引的代码片段信息删除
      await cg.indexCodeFile(file);
      logger.info(`index file ${file} ${action} done, 索引代码文件完成`);
    }
    logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'INDEXING_indexCodeFile',
      millis: Date.now() - indexStartTime,
      extra4: file,
      extra6: 'success'
    });
    logger.reportUserAction({
      key: 'INDEXING',
      type: 'indexCodeFile',
      subType: 'success',
      content: JSON.stringify({
        repo_dir: dirPath,
        file: file,
        time: Date.now() - indexStartTime
      })
    });
    indexStartTime = Date.now();
    await LanceDB.createIndex(collectionName);
    logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'INDEXING_createIndex_lance',
      millis: Date.now() - indexStartTime,
      extra4: file,
      extra6: 'success'
    });
    logger.reportUserAction({
      key: 'INDEXING',
      type: 'createIndex_lance',
      subType: 'success',
      content: JSON.stringify({
        repo_dir: dirPath,
        file: file,
        time: Date.now() - indexStartTime
      })
    });
    logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'index_file_' + action,
      millis: Date.now() - startTime,
      extra6: 'success'
    });
    logger.reportUserAction({
      key: 'INDEXING',
      type: 'index_file_' + action,
      subType: 'success',
      content: JSON.stringify({
        repo_dir: dirPath,
        file: file,
        time: Date.now() - startTime,
        action: action
      })
    });
    logger.info(`index file ${file} ${action} done, 索引完成`);
  } catch (error) {
    logger.error('index file failed, 索引文件失败', error);
    logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'index_file_' + action,
      millis: Date.now() - startTime,
      extra6: 'failed'
    });
    logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'unknownError',
      extra3: GlobalConfig.getConfig().getUsername(),
      extra4: GlobalConfig.getConfig().getRepoPath(),
      extra5: typeof error === 'object' ? (error as Error).message : String(error),
      extra6: new Error().stack || ''
    });
    throw error;
  } finally {
    await LanceDB.closeTable(collectionName);
  }
};

// 清除索引库
export const clearIndex = async (dirPath: string) => {
  try {
    const collectionName = generateCollectionName(dirPath);
    const result = await LanceDB.clearLanceDb(collectionName);
    if (!result) {
      logger.error(`clear index ${collectionName} failed`);
      return false;
    }
    logger.info(`clear index ${collectionName} done`);

    const codeInfotable = await CodeInfoTable.getCodeInfoTable(collectionName);
    await codeInfotable.dropTable();
    logger.info(`drop CodeInfoTable ${collectionName} done`);
    const nodeGraphTable = await NodeGraphTable.getNodeGraphTable(collectionName);
    await nodeGraphTable.dropTable();
    logger.info(`drop NodeGraphTable ${collectionName} done`);
    return true;
  } catch (error) {
    logger.error('clear index failed, 清除索引失败', error);
    return false;
  }
};
const indexFileFromCache = async (file: string, collectionName: string) => {
  try {
    // 判断缓存中是否已经存在，如果没有则创建
    const cacheInfo = await CacheStatusTable.findByFilePath(file);
    const nowHash = await getFileMd5(file);
    // 判断缓存中的 hash 和现在文件的 hash 是否一致
    if (cacheInfo && cacheInfo.file_md5 === nowHash) {
      const cacheTable = await LanceDB.getTable(getCacheCollectionName(collectionName));
      const query = cacheTable.query().where(`file_path = "${file}"`);
      const data = await LanceDB.getTableData(query);
      if (data && data.length > 0) {
        const table = await LanceDB.getTable(collectionName);
        await table.add(data);
        logger.info(`index file ${file} done, 从缓存中获取数据完成`);
        logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'index_file_from_cache',
          millis: 1,
          extra6: 'success'
        });
        return true;
      }
    }
  } catch (e) {
    logger.error('index file from cache failed, 从缓存中获取数据失败', e);
  }

  return false;
};
// 删除流程
const clearIndexByFilePath = async (filePath: string, dirPath: string) => {
  const collectionName = generateCollectionName(dirPath);

  try {
    const table = await LanceDB.getTable(collectionName);
    const condition = `file_path = "${filePath}"`;
    const query = table.query().where(condition); // Use exact matching for specific file paths
    const data = await LanceDB.getTableData(query);
    if (data.length === 0) {
      logger.info(`file ${filePath} not found in ${collectionName}`);
      return;
    }
    // 将数据插入到缓存表，同时记录文件信息 md5 和 文件路径
    // todo: 多次更改如何处理，需要先将之前的给清除掉
    const cacheTable = await LanceDB.getTable(getCacheCollectionName(collectionName));
    // 清除掉lance db 的cache table 之前的数据
    await cacheTable.delete(condition);
    const fileSet = new Set(data.map((item) => item.file_path));
    await cacheTable.add(data);
    fileSet.forEach(async (file) => {
      const hash = (await FileStatusTable.findByFilePath(file))?.hash ?? '';
      if (hash) {
        // 清除掉sqlite 的 cache table 之前的数据
        const exist = await CacheStatusTable.findByFilePath(file);
        if (exist) {
          if (Array.isArray(exist)) {
            exist.forEach(async (item) => {
              await CacheStatusTable.deleteById(item.id!);
            });
          } else {
            await CacheStatusTable.deleteById(exist.id!);
          }
        }
        await CacheStatusTable.create({
          filepath: file,
          delete_time: Date.now(),
          file_md5: hash,
          dir_path: dirPath
        });
      }
    });
    await table.delete(condition);
  } catch (error) {
    logger.error('将原数据放到缓存中失败，清除索引失败', error);
    try {
      const table = await LanceDB.getTable(collectionName);
      const condition = `file_path = "${filePath}"`;
      await table.delete(condition);
    } catch (error) {
      logger.error('直接清除索引失败', error);
    }
    throw error;
  }
};

// 修改流程
const modifyIndexByFilePath = async (filePath: string, dirPath: string) => {
  try {
    const collectionName = generateCollectionName(dirPath);
    const table = await LanceDB.getTable(collectionName);

    // 查询缓存表中是否有之前版本的缓存
    const cacheInfo = await CacheStatusTable.findByFilePath(filePath);
    if (!cacheInfo) {
      // 没有缓存
      return false;
    }

    const nowHash = await getFileMd5(filePath);
    if (nowHash !== cacheInfo.file_md5) {
      // 缓存文件与当前文件 hash 值不一样
      return false;
    }

    try {
      const cacheTable = await LanceDB.getTable(getCacheCollectionName(collectionName));

      // 1. 先从缓存表获取数据，并清理掉缓存表中的旧数据
      const cacheQuery = cacheTable.query().where(`file_path = "${filePath}"`);
      const cacheData = await LanceDB.getTableData(cacheQuery);
      if (cacheData && cacheData.length == 0) {
        // 没有缓存数据，则删除缓存状态
        await CacheStatusTable.deleteById(cacheInfo.id!);
        return false;
      }

      // 清理缓存表中的旧数据
      await LanceDB.deleteByCondition(getCacheCollectionName(collectionName), `file_path = "${filePath}"`);

      // 2. 从正式表获取当前数据并立即转移到缓存表
      const query = table.query().where(`file_path = "${filePath}"`);
      const currentData = await LanceDB.getTableData(query);
      if (currentData && currentData.length > 0) {
        await cacheTable.add(currentData);
        const hash = (await FileStatusTable.findByFilePath(filePath))?.hash ?? '';
        await CacheStatusTable.create({
          filepath: filePath,
          delete_time: Date.now(),
          file_md5: hash,
          dir_path: dirPath
        });
      }

      // 3. 删除正式表数据
      await LanceDB.deleteByCondition(collectionName, `file_path = "${filePath}"`);

      // 4. 缓存数据，恢复到正式表
      await table.add(cacheData);

      // 5. 更新缓存状态
      await CacheStatusTable.deleteById(cacheInfo.id!);

      return true;
    } catch (error) {
      logger.error('Error during modify index operation:', error);
      throw error;
    }
  } catch (error) {
    logger.error('modify index by file path failed, 修改索引失败', error);
    throw error;
  }
};
