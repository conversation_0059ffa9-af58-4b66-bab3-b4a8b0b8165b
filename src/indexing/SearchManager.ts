import { NonCodeEmbedding } from '@/embedding/NoneCodeEmbedding';
import { LanceDB } from '@/db/lancedb';
import { CodeEmbedding } from '@/embedding/CodeEmbedding';
import { Logger } from '@/util/log';
import { AGENT_NAMESPACE } from '@/util/const';
import { Api } from '@/http';
import { GlobalConfig } from '@/util/global';
import { RetrieveDataItem, SearchParams } from '@/http/types';
import { FileStatusTable } from '@/db/sqlite/tables/file-status';
import { convertToGitSshFormat } from '@/util/git-utils';
export class SearchManager {
  private collectionName: string;
  private codeDbEmbedding?: CodeEmbedding;
  private nonCodeDbEmbedding?: NonCodeEmbedding;
  private logger = new Logger('SearchManager');
  private httpClient = new Api();

  constructor(collectionName: string) {
    this.collectionName = collectionName;
    this.codeDbEmbedding = new CodeEmbedding(collectionName);
    this.nonCodeDbEmbedding = new NonCodeEmbedding(collectionName);
  }

  async search(params: SearchParams) {
    const {
      query,
      topK,
      targetDirectory,
      chatHistory,
      enable_rewrite = false,
      gitRepo,
      commit,
      username,
      enableCloudSearch = false
    } = params;

    const startTime = Date.now();
    const body = {
      query,
      chat_history: chatHistory,
      enable_rewrite
    };
    this.logger.debug(`queryEmbedding body, ${JSON.stringify(body)}`);
    const data = await this.httpClient.queryEmbedding(body);
    this.logger.info(`queryEmbedding result, ${JSON.stringify(data)}`);
    const { code_embedding, nl_embedding, rerank_query, sparse_embedding } = data;

    // 本地搜索
    let searchResult: RetrieveDataItem[] = [];
    try {
      searchResult = await this.retrieveData(topK, targetDirectory, code_embedding, nl_embedding, sparse_embedding);
      this.logger.info(`searchResult, ${JSON.stringify(searchResult)}`);
    } catch (error) {
      this.logger.error(`Error searching code vector: ${error}`);
    }

    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'search/retrieveDataSize',
      millis: Array.isArray(searchResult) ? searchResult.length : 0,
      extra3: Array.isArray(searchResult) ? String(searchResult.length) : '',
      extra4: targetDirectory.join(','),
      extra6: query
    });

    let finalSearchResult = searchResult;

    // 检查是否启用云端搜索并且有云端索引
    if (enableCloudSearch && gitRepo && username) {
      const cloudStartTime = Date.now();
      const hasCloud = await this.hasCloudIndex(gitRepo);
      if (hasCloud) {
        this.logger.info('Cloud index available, merging with local results');
        finalSearchResult = await this.getCloudIndexAndMerge({
          searchParams: { query, topK, targetDirectory, chatHistory, enable_rewrite },
          gitRepo,
          commit,
          username,
          localSearchResult: searchResult
        });
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'merge_cloud_and_local_search_result',
          millis: Date.now() - cloudStartTime,
          extra3: gitRepo,
          extra4: query,
          extra6: finalSearchResult.length.toString()
        });
      } else {
        this.logger.info('No cloud index available, using local results only', hasCloud, gitRepo);
      }
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'search/cloudSearchCost',
        millis: Date.now() - cloudStartTime,
        extra3: gitRepo,
        extra4: String(hasCloud),
        extra6: query
      });
    }

    const rerankBody = {
      query: rerank_query,
      code_context_list: finalSearchResult,
      top_k: topK
    };
    this.logger.debug(`rerankBody, ${JSON.stringify(rerankBody)}`);
    const rerankResult = await this.httpClient.codeRerank(rerankBody);
    this.logger.info(`rerankResult, ${JSON.stringify(rerankResult)}`);

    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'search/rerankSize',
      millis: Array.isArray(rerankResult?.code_context_list) ? rerankResult.code_context_list.length : 0,
      extra3: Array.isArray(rerankResult?.code_context_list) ? String(rerankResult.code_context_list.length) : '',
      extra4: targetDirectory.join(','),
      extra6: query
    });
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'search/search',
      millis: Date.now() - startTime,
      extra3: GlobalConfig.getConfig().getPlatform(),
      extra6: 'success'
    });
    return rerankResult;
  }
  async retrieveData(
    topK: number,
    targetDirectory: string[],
    CodeVector: number[] = [],
    NlVector: number[] = [],
    SparseVector: Record<string, number> = {},
    isCodeData = true
  ): Promise<RetrieveDataItem[]> {
    const outputFields = [
      'node_id',
      'snippet',
      'language',
      'sub_id',
      'is_sub_node',
      'code_type',
      'file_path',
      'name',
      'signature',
      'caller_references',
      'callee_references'
    ];

    const filterStr =
      targetDirectory.length > 0 ? targetDirectory.map((dir) => `file_path LIKE '%${dir}%'`).join(' OR ') : '';
    const initializer = isCodeData ? this.codeDbEmbedding : this.nonCodeDbEmbedding;
    const targetCollection = `${this.collectionName}`;

    if (!initializer) {
      throw new Error(`${isCodeData ? 'Code' : 'Non-code'} DB initializer is not initialized`);
    }

    const dataList: any[] = [];
    const startTime = Date.now();

    // Search based on code vector
    if (CodeVector.length > 0) {
      try {
        const table = await LanceDB.getCodeTable(targetCollection);
        let query = table.search(CodeVector, 'code_vector');

        if (filterStr) {
          query = query.where(filterStr);
        }
        query = query.limit(topK);
        const codeSearchRes = await query.select(outputFields);
        const list = await LanceDB.getTableData(codeSearchRes);
        this.logger.info(`codeSearchRes ${list.length}`);
        dataList.push(...list);
      } catch (error) {
        this.logger.error(`Error searching code vector: ${error}`);
      }
    }

    // Search based on nl vector
    if (NlVector.length > 0) {
      try {
        const table = await LanceDB.getCodeTable(targetCollection);
        let query = table.search(NlVector, 'nl_vector');

        if (filterStr) {
          query = query.where(filterStr);
        }
        query = query.limit(topK);
        const nlSearchRes = await query.select(outputFields);
        const list = await LanceDB.getTableData(nlSearchRes);
        this.logger.info(`nlSearchRes ${list.length}`);
        dataList.push(...list);
      } catch (error) {
        this.logger.error(`Error searching code vector: ${error}`);
      }
    }

    // Search based on sparse encoding
    if (SparseVector && Object.keys(SparseVector).length > 0) {
      try {
        // todo: 使用模糊搜索代替
        const table = await LanceDB.getCodeTable(targetCollection);
        const sparseSearchRes = await LanceDB.searchBySparseVector(table, SparseVector, outputFields, topK, filterStr);

        this.logger.info(`sparseSearchRes ${sparseSearchRes.length}`);

        dataList.push(...sparseSearchRes);
      } catch (error) {
        this.logger.error(`Error searching code vector: ${error}`);
      }
    }
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'retrieveDataCost',
      millis: Date.now() - startTime,
      extra3: topK.toString(),
      extra4: targetDirectory.join(',')
    });
    // Convert the final result to plain objects
    return dataList.map((item) => {
      return {
        node_id: item.node_id,
        sub_node_id: item.sub_id,
        is_sub_node: item.is_sub_node,
        code_content: item.snippet,
        metadata: {
          name: item.name,
          signature: item.signature,
          language: item.language,
          file_path: item.file_path,
          code_type: item.code_type,
          callers: item.caller_references,
          callees: item.callee_references
        }
      };
    });
  }

  /**
   * 判断是否存在云端索引
   * @param gitRepo git仓库地址，支持git和http协议
   * @returns 是否存在云端索引
   */
  async hasCloudIndex(gitRepo?: string): Promise<boolean> {
    if (!gitRepo) {
      this.logger.warn('No git repository provided for cloud index check.');
      return false;
    }

    try {
      // 将仓库地址转换为标准的 git SSH 格式
      const gitSshUrl = convertToGitSshFormat(gitRepo);
      if (!gitSshUrl) {
        this.logger.warn(`Failed to convert repo URL to git SSH format: ${gitRepo}`);
        return false;
      }

      // 使用 getLargeRepoIndexConfig 检查云端索引
      const indexConfig = await this.httpClient.getLargeRepoIndexConfig(gitSshUrl);

      if (indexConfig) {
        this.logger.info(`Found cloud index for repository: ${gitSshUrl}`);
        return true;
      }

      this.logger.info(`No cloud index found for repository: ${gitSshUrl}`);
      return false;
    } catch (error) {
      this.logger.error('Failed to check cloud index:', error);
      return false;
    }
  }

  /**
   * 获取云端索引并合并本地索引
   */
  async getCloudIndexAndMerge(params: {
    searchParams: SearchParams;
    gitRepo: string;
    commit?: string;
    username: string;
    localSearchResult: RetrieveDataItem[];
  }): Promise<RetrieveDataItem[]> {
    const { searchParams, gitRepo, commit, username, localSearchResult } = params;
    const { query, topK, targetDirectory } = searchParams;

    try {
      // 从gitRepo解析出repoName
      const repoName = this.parseRepoName(gitRepo);

      // 调用云端搜索API
      const cloudSearchBody = {
        files: [], // 不传文件信息
        searchTargetDirs: targetDirectory,
        query,
        codebaseSearch: true, // 必须为true
        commit: commit || '', // 可选，传入commit信息
        repoName,
        username,
        instantApplyMode: false,
        topK
      };

      const cloudSearchResult = await this.httpClient.cloudCodeSearch(cloudSearchBody);
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'search/cloudSearchResult',
        millis: Date.now(),
        extra3: query,
        extra6: cloudSearchResult.list.length.toString()
      });
      // 将云端搜索结果转换为 retrieveData 的结构
      const cloudDataForRerank = cloudSearchResult.list.map((item, index) => ({
        node_id: String(item.id),
        sub_node_id: 0,
        is_sub_node: false,
        code_content: item.code,
        metadata: {
          name: item.path.split('/').pop() || '',
          signature: '',
          language: item.language,
          file_path: item.path,
          code_type: '',
          callers: [],
          callees: []
        }
      }));

      // 并行过滤过时的云端文件
      const outdatedCheckPromises = cloudDataForRerank.map(async (item) => {
        const isOutdated = await this.isFileOutdated(item.metadata.file_path);
        return { item, isOutdated };
      });

      const outdatedCheckResults = await Promise.all(outdatedCheckPromises);
      const filteredCloudData = outdatedCheckResults
        .filter(({ isOutdated }) => {
          if (isOutdated) {
            this.logger.debug(`Filtered out outdated cloud file: ${isOutdated}`);
          }
          return !isOutdated;
        })
        .map(({ item }) => item);
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'cloud_search_outdated_check',
        millis: Date.now(),
        extra3: query,
        extra6: (cloudDataForRerank.length - filteredCloudData.length).toString()
      });
      // 合并本地和过滤后的云端结果
      const combinedData = [...localSearchResult, ...filteredCloudData];

      this.logger.info(
        `Cloud and local search merged, final result count: ${combinedData.length} (filtered ${
          cloudDataForRerank.length - filteredCloudData.length
        } outdated files)`
      );

      return combinedData;
    } catch (error) {
      this.logger.error('Failed to get cloud index and merge:', (error as Error).message);
      // 出错时返回本地搜索结果
      return localSearchResult;
    }
  }

  /**
   * 解析git仓库地址并转换协议
   * @param gitRepo 原始git仓库地址
   * @returns 转换后的URL数组（包含原始URL和转换协议后的URL）
   */
  private parseAndConvertRepoUrl(gitRepo: string): string[] {
    const urls: string[] = [gitRepo];

    try {
      // 处理git协议转http协议
      if (gitRepo.startsWith('git@')) {
        // **************:user/repo.git -> https://github.com/user/repo.git
        const match = gitRepo.match(/^git@([^:]+):(.+)$/);
        if (match) {
          const [, host, path] = match;
          const httpUrl = `https://${host}/${path}`;
          urls.push(httpUrl);
        }
      }
      // 处理git://协议转http协议
      else if (gitRepo.startsWith('git://')) {
        // git://github.com/user/repo.git -> https://github.com/user/repo.git
        const httpUrl = gitRepo.replace('git://', 'https://');
        urls.push(httpUrl);
      }
      // 处理http/https协议转git协议
      else if (gitRepo.startsWith('http://') || gitRepo.startsWith('https://')) {
        const url = new URL(gitRepo);
        // https://github.com/user/repo.git -> **************:user/repo.git
        const gitSshUrl = `git@${url.hostname}:${url.pathname.substring(1)}`;
        urls.push(gitSshUrl);

        // 同时添加git://协议版本
        const gitUrl = `git://${url.hostname}${url.pathname}`;
        urls.push(gitUrl);
      }
    } catch (error) {
      this.logger.warn(`Failed to parse repo URL: ${gitRepo}`, error);
    }

    return urls;
  }

  /**
   * 解析git仓库地址获取仓库名
   * @param gitRepo git仓库地址
   * @returns 仓库名（path部分）
   */
  private parseRepoName(gitRepo: string): string {
    try {
      // 处理git@格式: *************************:web-infra/ai-devops/kwaipilot-vscode-extension.git
      if (gitRepo.startsWith('git@')) {
        const match = gitRepo.match(/^git@[^:]+:(.+)$/);
        if (match) {
          let path = match[1];
          // 移除.git后缀
          if (path.endsWith('.git')) {
            path = path.slice(0, -4);
          }
          return path;
        }
      }
      // 处理http/https格式: https://git.corp.kuaishou.com/web-infra/ai-devops/kwaipilot-vscode-extension
      else if (gitRepo.startsWith('http://') || gitRepo.startsWith('https://')) {
        const url = new URL(gitRepo);
        let path = url.pathname.substring(1); // 移除开头的/
        // 移除.git后缀
        if (path.endsWith('.git')) {
          path = path.slice(0, -4);
        }
        return path;
      }
      // 处理git://格式
      else if (gitRepo.startsWith('git://')) {
        const url = new URL(gitRepo);
        let path = url.pathname.substring(1); // 移除开头的/
        // 移除.git后缀
        if (path.endsWith('.git')) {
          path = path.slice(0, -4);
        }
        return path;
      }
    } catch (error) {
      this.logger.warn(`Failed to parse repo name from: ${gitRepo}`, error);
    }

    // 解析失败时返回原始URL作为备选
    return gitRepo;
  }

  /**
   * 检测文件是否过时
   * @param filePath 文件路径
   * @returns 文件是否过时
   */
  private async isFileOutdated(filePath: string): Promise<boolean> {
    // TODO: 实现文件过时检测逻辑
    try {
      this.logger.debug(`Checking if file is outdated: ${filePath}`);
      const result = await FileStatusTable.checkFileStatus(filePath);
      return result;
    } catch (error) {
      this.logger.warn(`Failed to check if file is outdated: ${filePath}`, error);
      return false; // 出错时默认保留
    }
  }
}
