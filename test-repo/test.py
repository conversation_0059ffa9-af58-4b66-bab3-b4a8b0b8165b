class Calculator:
    def __init__(self):
        self.result = 0

    def add(self, number):
        self.result += number
        return self

    def subtract(self, number):
        self.result -= number
        return self

    def reset(self):
        self.result = 0
        return self

    def get_result(self):
        return self.result
    
    def get_hash(self, filepath):
        with open(filepath, 'rb') as file:
            content = file.read()
            return hashlib.md5(content).hexdigest()
        
    def get_file_status(self, filepath):
        return FileStatusTable.findByFilePath(filepath)
