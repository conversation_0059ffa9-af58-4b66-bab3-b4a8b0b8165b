{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sort(array):\n", "    return sorted(array)\n", "\n", "def reverse(array):\n", "    return array[::-1]\n", "\n", "for i in range(10):\n", "    print(i)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}