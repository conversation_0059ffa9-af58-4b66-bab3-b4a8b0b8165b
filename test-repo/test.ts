class Calculator {
  private result: number;

  constructor() {
    this.result = 0;
  }

  add(number: number): Calculator {
    this.result += number;
    return this;
  }

  subtract(number: number): Calculator {
    this.result -= number;
    return this;
  }

  multiply(number: number): Calculator {
    this.result *= number;
    return this;
  }

  divide(number: number): Calculator {
    if (number === 0) {
      throw new Error("Cannot divide by zero");
    }
    this.result /= number;
    return this;
  }

  getResult(): number {
    return this.result;
  }

  /**
 * 重置计算器的状态。
 * 
 * 将当前的结果值重置为0，并返回当前的计算器实例，
 * 以便可以链式调用其他方法。
 * 
 * @returns {Calculator} 返回当前的计算器实例。
 */
reset(): Calculator {
    this.result = 0; // 将结果值重置为0
    return this; // 返回当前的计算器实例
}
}

export default Calculator;