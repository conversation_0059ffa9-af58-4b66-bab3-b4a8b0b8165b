const http = require('http');

const server = http.createServer((req, res) => {
  let body = '';

  req.on('data', (chunk) => {
    console.log(chunk);
    body += chunk;
  });
  

  req.on('end', () => {
    let responseData = { msg: '000' };

    if (req.url === '/aa') {
      try {
        const requestBody = JSON.parse(body);
        console.log(requestBody);
        if (requestBody.a === 1) {
          responseData = { msg: '111' };
        }
      } catch (error) {
        console.error(body);
      }
    }

    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(responseData));
  });
});

const PORT = 7777;

server.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}/`);
  console.log('123')
});