const path = require('path')
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const { CleanWebpackPlugin } = require('clean-webpack-plugin')
const RadarSeedScriptPlguin = require('@ks-dmo/radar-seed-webpack-plugin')
const BlankScreenDetectorWebpack = require('@ad/blank-screen-detector-webpack');

module.exports = {
  lintOnSave: false,
  chainWebpack: config => {
    // 防止watcher太多
    config.watchOptions({
      ignored: ['node_modules/**']
    });
    // vue 使用script 引入
    config.resolve.alias
      .set('vue$', path.resolve(__dirname, 'src/common/modules/vue.js'))
      .set('element-ui$', path.resolve(__dirname, 'src/common/modules/element-ui.js'))

    config.plugin('html')
      .tap(options => {
        options[0].template = path.resolve(__dirname, './public/index.html');
        return options;
      });
    config.module
      .rule('vue')
      .use('vue-loader')
      .tap(args => {
        args.compilerOptions.whitespace = 'preserve'
      })
      // 只有production才有意义
      if (process.env.GEN_WEBPACK_BUNDLE_ANALYZER && process.env.NODE_ENV === 'production') {
        config
          .plugin('analyzer')
          .use(BundleAnalyzerPlugin, [{ analyzerMode: 'static', openAnalyzer: true }]);
      }


  },
  devServer: {
    port: 8079,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:1901'
      },
      '/socket.io/': {
        target: 'http://127.0.0.1:1901'
      },
      // '/api/': {
      //   target: 'https://kapi.corp.kuaishou.com/mock/3858/'
      // }
    },
    disableHostCheck: true
  },
  outputDir: '../server/public',
configureWebpack: {
      output: {
        // filename: 'app.js',  // +++ 打包的文件不再需要 hash
        // path: path.resolve(__dirname, '../server/public')
      },
      plugins: [
        // new HtmlWebpackPlugin({
        //   template: path.resolve(__dirname, './public/index.html')
        // }),
        /** 这里引入白屏检测的插件逻辑 */
        new BlankScreenDetectorWebpack({
          /** 这里写的白屏检测的容器，根据项目具体配置，更多参数参照上面的文档 */
          containers: ['#app']
        }),
        /** 这里引入种子包插件 */
        new RadarSeedScriptPlguin({
            projectId: "3e73a208dd", // 雷达项目 id
            env: "production"
        }),
        new CleanWebpackPlugin(),
      ]
  }
}
