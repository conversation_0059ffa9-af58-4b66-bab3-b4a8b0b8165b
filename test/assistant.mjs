import net from 'net';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs/promises';

const isSilent = !!process.env.SILENT;
const verboseLog = (...args) => !isSilent && console.log(...args);
const commonLog = (...args) => console.log(...args);
const errorLog = (...args) => console.error(...args);

// Get command line arguments (skip first two arguments as they are node and script path)
const args = process.argv.slice(2);
verboseLog('Command line arguments:', args);

const client = new net.Socket();
// const dirPath = '/Users/<USER>/Documents/work/kwaipilot-fe';
// const dirPath = '/Users/<USER>/Documents/work/ide-agent/test-repo';
const dirPath = '/Users/<USER>/Documents/code/ide-agent';
// const dirPath = '/Users/<USER>/Documents/work/中文/web-reader-for-llm';

client.connect(3000, '127.0.0.1', async () => {
  commonLog('Connecting to server');
});
client.on('connect', () => {
  commonLog('Connected to server');
  const commonInfo = {
    pluginVersion: '1.0.26', // 插件版本
    version: '1.0.26', // IDE 版本
    platform: 'vscode', // IDE 平台
    repo: {
      git_url: 'https://github.com/kuaishou/kproxy.git', // 完整git地址
      dir_path: dirPath, // 目录地址
      commit: 'undefined' // git commit id
    },
    cwd: dirPath
  };

  // 发送消息示例
  const message = {
    common: commonInfo,
    messageType: 'state/agentState',
    data: {
      msg: 'ping'
    },
    messageId: uuidv4()
  };

  client.write(JSON.stringify(message) + '\r\n');
  if (args.length > 0) {
    commonLog('First argument:', args[0]);
    if (args[0] === 'assistant') {
      makeAssistantRequest(message);
    }
    if (args[0] === 'index') {
      indexRepo(message);
    }
    if (args[0] === 'clear') {
      clearIndex(message);
    }
    if (args[0] === 'search') {
      makeSearch(message);
    }
    if (args[0] === 'file') {
      const file = '/Users/<USER>/Documents/work/ide-agent/test-repo/napi_init.cpp';
      indexFiles(message, file);
    }
  }
});

const makeAssistantRequest = async (message) => {
  try {
    message.messageType = 'assistant/agent/local';
    message.messageId = uuidv4();
    message.data = {
      type: 'newTask',
      // task: "帮我执行一下删除 readme 文件命令",
      // task: "解释一下indexManager的作用",
      // 帮我修改src/agent/agent.ts文件，在文件的第100行添加一个注释，注释内容为："这是一个测试"
      task: `在src目录中搜索startTask函数的作用`,
      rules: ['.kwaipilot/rules/a.md', '.kwaipilot/rules/d.md'],
      reqData: {
        sessionId: '11111',
        chatId: '1111',
        messages: [],
        useSearch: false,
        username: 'liuzhengzheng',
        projectInfo: {
          projectName: 'web-infra/ai-devops/ide-agent',
          gitUrl: '*************************:web-infra/ai-devops/ide-agent.git',
          openedFilePath: 'src/agent/agent.ts'
        },
        deviceInfo: {
          deviceId: '',
          platform: 'kwaipilot-vscode',
          pluginVersion: '8.3.0',
          deviceName: 'liuzhengzhengdeMacBook-Pro-2.local',
          deviceModel: 'darwin',
          deviceOsVersion: '24.3.0',
          deviceOsName: 'macOS',
          ide: 'vscode',
          ideVersion: '1.85.1'
        }
      },
      localMessages: []
    };
    // message.data = {
    //   type: 'restore',
    //   params: {
    //     restoreCommitHash: 'cb794d36078fdd4d3a599599138d65bfff9c80e0',
    //     sessionId: 'rfdxyl3zli01lpdgc55d',
    //     chatId: 'm8cvoaq8be4tmoccmp5'
    //   }
    // };
    client.write(JSON.stringify(message) + '\r\n');
    // setTimeout(() => {
    //   message.data = { type: "stop" };
    //   client.write(JSON.stringify(message) + "\r\n");
    // }, 3000);
  } catch (error) {
    errorLog('Error while indexing files:', error);
  }
};

const indexRepo = async (message) => {
  try {
    message.messageType = 'index/repoIndex';
    message.messageId = uuidv4();
    client.write(JSON.stringify(message) + '\r\n');
  } catch (error) {
    errorLog('Error while indexing files:', error);
  }
};
const indexFiles = async (message, filePath) => {
  message.messageType = 'index/file';
  message.messageId = uuidv4();
  const options = {
    file: filePath,
    action: 'create'
  };
  message.data = options;
  client.write(JSON.stringify(message) + '\r\n');
};

const makeSearch = (message) => {
  return new Promise((resolve, reject) => {
    message.messageType = 'search/search';
    message.messageId = uuidv4();
    const options = {
      query: '#{repo} markdwon\n\n',
      topK: 10,
      chatHistory: [],
      targetDirectory: ['packages/server/src/middlewares']
    };
    message.data = options;
    client.write(JSON.stringify(message) + '\r\n');
  });
};

const makeSearchByPath = (message) => {
  message.messageType = 'search/searchByPath';
  message.messageId = uuidv4();
  const options = {
    query: '.',
    topK: 100,
    vercel: [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
    targetDirectory: ['apps/core/code/analyze']
  };
  message.data = options;
  client.write(JSON.stringify(message) + '\r\n');
};

const buildIndex = (message) => {
  message.messageType = 'index/repoIndex';
  message.messageId = uuidv4();
  client.write(JSON.stringify(message) + '\r\n');
};

const pauseIndex = (message) => {
  message.messageType = 'index/pause';
  message.messageId = uuidv4();
  client.write(JSON.stringify(message) + '\r\n');
};

const startIndex = (message) => {
  message.messageType = 'index/build';
  message.messageId = uuidv4();
  client.write(JSON.stringify(message) + '\r\n');
};

const clearIndex = (message) => {
  message.messageType = 'index/clearIndex';
  message.messageId = uuidv4();
  const options = {};
  message.data = options;
  client.write(JSON.stringify(message) + '\r\n');
};

let lastPrintedMessage = '';
async function handleMessage(data) {
  try {
    const arr = data.split('\r\n');
    for (const item of arr) {
      if (item) {
        const message = JSON.parse(item);
        if (
          message.messageType === 'assistant/agent/message' ||
          message.messageType === 'assistant/agent/messageList'
        ) {
          const messageData = message.data;

          const printEasyMessage = () => {
            const printedMessage = `[${messageData.say}] ${messageData.text}`;
            if (printedMessage.startsWith(lastPrintedMessage)) {
              const additionalText = printedMessage.slice(lastPrintedMessage.length);
              process.stdout.write(additionalText);
            } else {
              process.stdout.write(printedMessage + '\n\n');
            }
            lastPrintedMessage = printedMessage;
          };

          if (isSilent) {
            printEasyMessage();
          } else {
            commonLog('Received:', messageData.text);
          }
        }
        if (message.messageId && message.messageType === 'config/getIdeSetting') {
          message.data = {
            data: {
              dirPath: dirPath
            }
          };
          verboseLog(message);
          client.write(JSON.stringify(message) + '\r\n');
        }
        if (message.messageId && message.messageType === 'state/ideState') {
          message.status = 'ok';
          verboseLog(message);
          client.write(JSON.stringify(message) + '\r\n');
        }
        if (message.messageId && message.messageType === 'state/ideInfo') {
          if (!message.data || !message.data.data) {
            message.data = {
              data: {
                repoInfo: {
                  dir_path: dirPath,
                  git_url: 'https://github.com/kuaishou/kproxy.git',
                  commit: 'undefined'
                },
                userInfo: {
                  name: 'renbeihai'
                }
              }
            };
          }
          message.data.data.proxyUrl = 'https://qa-kinsight.staging.kuaishou.com/';
          message.data.data.ideVersion = '1.0.26';
          message.data.data.pluginVersion = '1.0.26';
          message.data.data.platform = 'vscode';

          verboseLog(message);
          client.write(JSON.stringify(message) + '\r\n');
        }
        if (message.messageType === 'assistant/agent/message') {
          verboseLog(message.messageType, message.data);
        }
        if (message.messageType === 'assistant/agent/messageList') {
          verboseLog(message.messageType, message.data);
        }
        if (message.messageType === 'assistant/agent/environment') {
          message.data =
            '<environment_details>\n# VSCode Visible Files\nsrc/agent/agent.ts\n\n# VSCode Open Tabs\nsrc/core/index.ts\nsrc/protocol/ideCore.ts\ntest/client.mjs\nsrc/agent/responses.ts\nsrc/agent/agent.ts\npackage.json\nsrc/agent/system.ts\nsrc/agent/types/message.ts\nsrc/agent/types/type.d.ts\n\n# Current Time\n2025/3/17 下午5:49:38 (Asia/Shanghai, UTC+8:00)\n\n# Current Working Directory (/Users/<USER>/Documents/code/ide-agent) Files\n.eslintrc.json\n.gitignore\nimportMetaUrl.js\njest.config.js\njest.global-setup.ts\njest.setup-after-env.ts\npackage-lock.json\npackage.json\nREADME.md\ntsconfig.json\npkgJson/\npkgJson/darwin-arm64/\npkgJson/darwin-arm64/package-lock.json\npkgJson/darwin-arm64/package.json\npkgJson/darwin-x64/\npkgJson/darwin-x64/package-lock.json\npkgJson/darwin-x64/package.json\npkgJson/linux-arm64/\npkgJson/linux-arm64/package-lock.json\npkgJson/linux-arm64/package.json\npkgJson/linux-x64/\npkgJson/linux-x64/package-lock.json\npkgJson/linux-x64/package.json\npkgJson/win32-arm64/\npkgJson/win32-arm64/package-lock.json\npkgJson/win32-arm64/package.json\npkgJson/win32-x64/\npkgJson/win32-x64/package-lock.json\npkgJson/win32-x64/package.json\nscripts/\nscripts/build.js\nscripts/build.sh\nscripts/uninstall.js\nscripts/util/\nscripts/util/index.js\nsrc/\nsrc/index.ts\nsrc/agent/\nsrc/agent/agent.ts\nsrc/agent/parse-assistant-message.ts\nsrc/agent/responses.ts\nsrc/agent/system.ts\nsrc/agent/tools/\nsrc/agent/tools/extract-text.ts\nsrc/agent/tools/list-files.ts\nsrc/agent/types/\nsrc/agent/types/message.ts\nsrc/agent/types/type.d.ts\nsrc/code/\nsrc/code/analyze/\nsrc/code/analyze/code_utils.ts\nsrc/code/analyze/ContextGraph.ts\nsrc/code/analyze/html_utils.ts\nsrc/code/analyze/program_utils.ts\nsrc/code/analyze/util.ts\nsrc/code/chunk/\nsrc/code/chunk/code.ts\nsrc/code/chunk/Tokenlizer.ts\nsrc/code/node/\nsrc/code/node/ContextNode.ts\nsrc/code/node/TextChunkNode.ts\nsrc/code/node/VariableNode.ts\nsrc/core/\nsrc/core/IdeStateManager.ts\nsrc/core/index.ts\nsrc/db/\nsrc/db/lancedb/\nsrc/db/lancedb/index.ts\nsrc/db/sqlite/\nsrc/db/sqlite/index.ts\nsrc/db/sqlite/schema.sql\nsrc/db/sqlite/tables/\nsrc/db/sqlite/tables/cache-status.ts\nsrc/db/sqlite/tables/code-info.ts\nsrc/db/sqlite/tables/file-status.ts\nsrc/db/sqlite/tables/index.ts\nsrc/db/sqlite/tables/node-graph.ts\nsrc/db/sqlite/tables/repo-status.ts\nsrc/db/sqlite/tables/task.ts\nsrc/db/sqlite/tables/user-status.ts\nsrc/embedding/\nsrc/embedding/CodeEmbedding.ts\nsrc/embedding/EmbeddingBase.ts\nsrc/embedding/EmbeddingModel.ts\nsrc/embedding/NoneCodeEmbedding.ts\nsrc/entry/\nsrc/entry/IpcMessenger.ts\nsrc/entry/logging.ts\nsrc/entry/TcpMessenger.ts\nsrc/http/\nsrc/http/index.ts\nsrc/http/types.ts\nsrc/index-manager/\nsrc/index-manager/CacheManager.ts\nsrc/index-manager/FileSystemHelper.ts\nsrc/index-manager/GitManager.ts\nsrc/index-manager/index.ts\nsrc/index-manager/types.ts\nsrc/indexing/\nsrc/indexing/index.ts\nsrc/indexing/SearchManager.ts\nsrc/protocol/\nsrc/protocol/ideCore.ts\nsrc/protocol/index.d.ts\nsrc/protocol/index.ts\nsrc/protocol/messenger.ts\nsrc/tree-sitter/\nsrc/tree-sitter/TreeSitter.ts\nsrc/tree-sitter/code-snippet-queries/\nsrc/tree-sitter/code-snippet-queries/c_sharp.scm\nsrc/tree-sitter/code-snippet-queries/c.scm\nsrc/tree-sitter/code-snippet-queries/cpp.scm\nsrc/tree-sitter/code-snippet-queries/elisp.scm\nsrc/tree-sitter/code-snippet-queries/elixir.scm\nsrc/tree-sitter/code-snippet-queries/elm.scm\nsrc/tree-sitter/code-snippet-queries/go.scm\nsrc/tree-sitter/code-snippet-queries/html.scm\nsrc/tree-sitter/code-snippet-queries/java.scm\nsrc/tree-sitter/code-snippet-queries/javascript.scm\nsrc/tree-sitter/code-snippet-queries/kotlin.scm\nsrc/tree-sitter/code-snippet-queries/ocaml.scm\nsrc/tree-sitter/code-snippet-queries/php.scm\nsrc/tree-sitter/code-snippet-queries/python.scm\nsrc/tree-sitter/code-snippet-queries/ql.scm\nsrc/tree-sitter/code-snippet-queries/ruby.scm\nsrc/tree-sitter/code-snippet-queries/rust.scm\nsrc/tree-sitter/code-snippet-queries/tsx.scm\nsrc/tree-sitter/code-snippet-queries/typescript.scm\nsrc/util/\nsrc/util/array.ts\nsrc/util/const.ts\nsrc/util/file.ts\nsrc/util/global.ts\nsrc/util/log-utils.ts\nsrc/util/log.d.ts\nsrc/util/log.ts\nsrc/util/notifications.ts\nsrc/util/path.ts\nsrc/util/paths.ts\nsrc/util/verify.ts\nsrc/util/weblogger.ts\ntest/\ntest/binary.test.ts\ntest/client.mjs\ntest-repo/\ntest-repo/AdvancedPage.tsx\ntest-repo/Calculator.java\ntest-repo/component-service-tree.es.js\ntest-repo/config.yaml\ntest-repo/data.json\ntest-repo/Dockerfile\ntest-repo/example.ipynb\ntest-repo/napi_init.cpp\ntest-repo/program.cs\ntest-repo/query.sql\ntest-repo/readme.md\ntest-repo/requirements.txt\ntest-repo/s.js\ntest-repo/test.css\ntest-repo/test.csv\ntest-repo/test.html\ntest-repo/test.js\ntest-repo/test.kt\ntest-repo/test.php\ntest-repo/test.py\ntest-repo/test.rb\ntest-repo/test.rs\ntest-repo/test.sh\ntest-repo/test.ts\ntest-repo/vue.config.js\ntest-repo/nested-folder/\ntest-repo/nested-folder/helloNested.py\ntest-repo/nested-folder/package-lock.json\ntest-repo/nested-folder/package.json\n\n# Current Mode\nACT MODE\n</environment_details>';
          client.write(JSON.stringify(message) + '\r\n');
        }
      }
    }
    return 'pong';
  } catch (e) {
    errorLog(e);
  }
}

client.on('data', (data) => {
  verboseLog('Received:', data.toString());
  handleMessage(data.toString());
});

client.on('close', () => {
  commonLog('Connection closed');
});

client.on('error', (err) => {
  errorLog('Connection error:', err);
});
