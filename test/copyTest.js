const fs = require('fs');
const fsp = require('fs/promises');
const { execSync } = require('child_process');

// 文件路径
const SOURCE_FILE = '/Users/<USER>/.kwaipilot/index/index.sqlite-wal'; // 大文件（建议100MB+）
const DEST_PREFIX = '/Users/<USER>/.kwaipilot/index/index.sqlite.tmp';

// 时间测量工具
function measureSync(fn) {
  const start = process.hrtime.bigint();
  fn();
  const end = process.hrtime.bigint();
  return Number(end - start) / 1e6; // 转为毫秒
}

async function measureAsync(fn) {
  const start = process.hrtime.bigint();
  await fn();
  const end = process.hrtime.bigint();
  return Number(end - start) / 1e6;
}

// 各种复制方式
function copyUsingSync() {
  fs.copyFileSync(SOURCE_FILE, `${DEST_PREFIX}sync.bin`);
}

async function copyUsingAsync() {
  await fsp.copyFile(SOURCE_FILE, `${DEST_PREFIX}async.bin`);
}

function copyUsingExec() {
  execSync(`cp ${SOURCE_FILE} ${DEST_PREFIX}exec.bin`);
}

// 执行测试
(async () => {
  console.log('开始性能测试...\n');

  const timeSync = measureSync(copyUsingSync);
  console.log(`fs.copyFileSync 用时:   ${timeSync.toFixed(2)} ms`);

  const timeAsync = await measureAsync(copyUsingAsync);
  console.log(`fs.promises.copyFile 用时: ${timeAsync.toFixed(2)} ms`);

  const timeExec = measureSync(copyUsingExec);
  console.log(`execSync("cp") 用时:     ${timeExec.toFixed(2)} ms`);

  console.log('\n测试完成 ✅');
})();
