import { BufferDataHandle } from '../src/util/messenger';

describe('BufferDataHandle', () => {
  let bufferDataHandle: BufferDataHandle;
  let onLineMock: jest.Mock;

  beforeEach(() => {
    bufferDataHandle = new BufferDataHandle();
    onLineMock = jest.fn();
  });

  afterEach(() => {
    onLineMock.mockClear();
    bufferDataHandle.clear();
  });

  it('should handle a single complete line', () => {
    const message = 'Hello World';
    const data = Buffer.from(message + '\r\n');

    bufferDataHandle.handleData(data, onLineMock);

    expect(onLineMock).toHaveBeenCalledTimes(1);
    expect(onLineMock).toHaveBeenCalledWith(message);
  });

  it('should handle multiple complete lines', () => {
    const messages = ['Line 1', 'Line 2', 'Line 3'];
    const data = Buffer.from(messages.join('\r\n') + '\r\n');

    bufferDataHandle.handleData(data, onLineMock);

    expect(onLineMock).toHaveBeenCalledTimes(3);
    messages.forEach((msg, index) => {
      expect(onLineMock).toHaveBeenNthCalledWith(index + 1, msg);
    });
  });

  it('should handle incomplete lines across multiple chunks', () => {
    const message = 'Hello World';
    const firstChunk = Buffer.from('Hel');
    const secondChunk = Buffer.from('lo Wo');
    const thirdChunk = Buffer.from('rld\r\n');

    bufferDataHandle.handleData(firstChunk, onLineMock);
    bufferDataHandle.handleData(secondChunk, onLineMock);
    bufferDataHandle.handleData(thirdChunk, onLineMock);

    expect(onLineMock).toHaveBeenCalledTimes(1);
    expect(onLineMock).toHaveBeenCalledWith(message);
  });

  it('should handle multiple incomplete JSON messages', () => {
    const messages = [
      { type: 'message1', data: 'test1' },
      { type: 'message2', data: 'test2' }
    ];

    const firstChunk = Buffer.from(JSON.stringify(messages[0]) + '\r');
    const secondChunk = Buffer.from('\n' + JSON.stringify(messages[1]) + '\r\n');

    bufferDataHandle.handleData(firstChunk, onLineMock);
    bufferDataHandle.handleData(secondChunk, onLineMock);

    expect(onLineMock).toHaveBeenCalledTimes(2);
    messages.forEach((msg, index) => {
      expect(onLineMock).toHaveBeenNthCalledWith(index + 1, JSON.stringify(msg));
    });
  });

  it('should handle empty chunks', () => {
    const emptyBuffer = Buffer.alloc(0);
    bufferDataHandle.handleData(emptyBuffer, onLineMock);
    expect(onLineMock).not.toHaveBeenCalled();
  });

  it('should handle chunks with only line endings', () => {
    const data = Buffer.from('\r\n\r\n\r\n');
    bufferDataHandle.handleData(data, onLineMock);
    expect(onLineMock).not.toHaveBeenCalled();
  });

  it('should handle large messages', () => {
    const largeMessage = 'x'.repeat(1024 * 1024); // 1MB message
    const data = Buffer.from(largeMessage + '\r\n');

    bufferDataHandle.handleData(data, onLineMock);

    expect(onLineMock).toHaveBeenCalledTimes(1);
    expect(onLineMock).toHaveBeenCalledWith(largeMessage);
  });

  it('should handle mixed complete and incomplete lines', () => {
    const firstChunk = Buffer.from('Line1\r\nLine2\r\nLin');
    const secondChunk = Buffer.from('e3\r\nLine4\r\n');

    bufferDataHandle.handleData(firstChunk, onLineMock);
    expect(onLineMock).toHaveBeenCalledTimes(2);
    expect(onLineMock).toHaveBeenNthCalledWith(1, 'Line1');
    expect(onLineMock).toHaveBeenNthCalledWith(2, 'Line2');

    bufferDataHandle.handleData(secondChunk, onLineMock);
    expect(onLineMock).toHaveBeenCalledTimes(4);
    expect(onLineMock).toHaveBeenNthCalledWith(3, 'Line3');
    expect(onLineMock).toHaveBeenNthCalledWith(4, 'Line4');
  });

  it('should handle large fragmented JSON messages', () => {
    // 创建一个大型的嵌套 JSON 对象
    const createLargeObject = (depth: number, size: number): any => {
      if (depth === 0) {
        return 'x'.repeat(size);
      }
      const obj: any = {};
      for (let i = 0; i < 5; i++) {
        obj[`key${i}`] = createLargeObject(depth - 1, size);
      }
      return obj;
    };

    const messages = [
      { type: 'large1', data: createLargeObject(3, 1000) },
      { type: 'large2', data: createLargeObject(3, 1000) }
    ];

    const fullData = messages.map(msg => JSON.stringify(msg)).join('\r\n') + '\r\n';
    const chunkSize = 1024; // 1KB chunks
    const chunks: Buffer[] = [];

    // 将数据分成多个小块
    for (let i = 0; i < fullData.length; i += chunkSize) {
      chunks.push(Buffer.from(fullData.slice(i, i + chunkSize)));
    }

    // 逐个处理数据块
    chunks.forEach(chunk => {
      bufferDataHandle.handleData(chunk, onLineMock);
    });

    // 验证结果
    expect(onLineMock).toHaveBeenCalledTimes(2);
    messages.forEach((msg, index) => {
      expect(onLineMock).toHaveBeenNthCalledWith(index + 1, JSON.stringify(msg));

      // 验证接收到的 JSON 是否可以正确解析
      const receivedMsg = JSON.parse(onLineMock.mock.calls[index][0]);
      expect(receivedMsg).toEqual(msg);
    });
  });

  it('should handle consecutive incomplete chunks without line endings', () => {
    const chunks = [
      Buffer.from('{"id":1'),
      Buffer.from(',"name":"test"'),
      Buffer.from('}\r\n')
    ];

    chunks.forEach(chunk => {
      bufferDataHandle.handleData(chunk, onLineMock);
    });

    expect(onLineMock).toHaveBeenCalledTimes(1);
    expect(onLineMock).toHaveBeenCalledWith('{"id":1,"name":"test"}');

    // 验证 JSON 解析
    const parsed = JSON.parse(onLineMock.mock.calls[0][0]);
    expect(parsed).toEqual({ id: 1, name: 'test' });
  });

  it('should handle special characters in messages', () => {
    const specialChars = '{"special":"🎉\\n\\r\\t\\u0000\\u001f"}';
    const data = Buffer.from(specialChars + '\r\n');

    bufferDataHandle.handleData(data, onLineMock);

    expect(onLineMock).toHaveBeenCalledTimes(1);
    expect(onLineMock).toHaveBeenCalledWith(specialChars);

    // 验证 JSON 解析
    const parsed = JSON.parse(onLineMock.mock.calls[0][0]);
    // JSON.parse 会自动解析转义字符，所以我们期望看到实际的字符而不是转义序列
    expect(parsed.special).toBe('🎉\n\r\t\u0000\u001f');
  });

  it('should handle invalid UTF-8 sequences gracefully', () => {
    const validData = Buffer.from('valid\r\n');
    const invalidData = Buffer.concat([
      Buffer.from([0xFF, 0xFE, 0xFD]), // 无效的 UTF-8 序列
      Buffer.from('\r\n')
    ]);
    const moreValidData = Buffer.from('more-valid\r\n');

    bufferDataHandle.handleData(validData, onLineMock);
    bufferDataHandle.handleData(invalidData, onLineMock);
    bufferDataHandle.handleData(moreValidData, onLineMock);

    // 由于无效的 UTF-8 序列会被忽略，所以只会有两次有效的调用
    expect(onLineMock).toHaveBeenCalledTimes(2);
    expect(onLineMock).toHaveBeenNthCalledWith(1, 'valid');
    expect(onLineMock).toHaveBeenNthCalledWith(2, 'more-valid');
  });

  it('should handle multiple unfinished lines correctly', () => {
    const message = { id: 1, data: 'x'.repeat(1000) };
    const jsonStr = JSON.stringify(message);

    // 将消息分成多个小块，确保每个块都不包含完整的行结束符
    const chunks: Buffer[] = [];
    const chunkSize = 10;

    for (let i = 0; i < jsonStr.length; i += chunkSize) {
      chunks.push(Buffer.from(jsonStr.slice(i, i + chunkSize)));
    }
    // 最后添加行结束符
    chunks.push(Buffer.from('\r\n'));

    // 逐个处理数据块
    chunks.forEach(chunk => {
      bufferDataHandle.handleData(chunk, onLineMock);
    });

    expect(onLineMock).toHaveBeenCalledTimes(1);
    const receivedMsg = JSON.parse(onLineMock.mock.calls[0][0]);
    expect(receivedMsg).toEqual(message);
  });

  it('should handle empty or invalid input gracefully', () => {
    // @ts-ignore 测试无效输入
    bufferDataHandle.handleData(null, onLineMock);
    bufferDataHandle.handleData(Buffer.alloc(0), onLineMock);
    // @ts-ignore 测试无效输入
    bufferDataHandle.handleData(undefined, onLineMock);

    expect(onLineMock).not.toHaveBeenCalled();
  });

  it('should maintain unfinished line state correctly', () => {
    const firstChunk = Buffer.from('{"id":1');
    bufferDataHandle.handleData(firstChunk, onLineMock);

    // 验证未完成的行被正确保存
    expect(bufferDataHandle.getUnfinishedLine().toString()).toBe('{"id":1');

    const secondChunk = Buffer.from('}\r\n');
    bufferDataHandle.handleData(secondChunk, onLineMock);

    // 验证未完成的行被清空
    expect(bufferDataHandle.getUnfinishedLine().length).toBe(0);

    expect(onLineMock).toHaveBeenCalledTimes(1);
    expect(onLineMock).toHaveBeenCalledWith('{"id":1}');
  });
});