import { ContextManager } from '../../../src/agent/context/ContextManager';
import { TokenCalculator } from '../../../src/agent/types/context';
import { MessageParamVersion1 } from '../../../src/agent/types/type.d';

describe('ContextManager - groupAgentMessages', () => {
  let contextManager: ContextManager;

  // Mock dependencies
  const mockTokenCalculator: TokenCalculator = {
    calculate: jest.fn().mockResolvedValue(10)
  };

  beforeEach(() => {
    contextManager = new ContextManager(1000, mockTokenCalculator);
  });

  it('应该正确对没有chatId的消息进行分组', () => {
    const messages: MessageParamVersion1[] = [
      { role: 'user', content: 'message1', version: 1, chatId: '' },
      { role: 'assistant', content: 'message2', version: 1, chatId: '' }
    ];

    const result = contextManager.groupAgentMessages(messages);

    expect(result).toHaveLength(2);
    expect(result[0]).toHaveLength(1);
    expect(result[1]).toHaveLength(1);
    expect(result[0][0].content).toBe('message1');
    expect(result[1][0].content).toBe('message2');
  });

  it('应该正确对有相同chatId的消息进行分组', () => {
    const messages: MessageParamVersion1[] = [
      { role: 'user', content: 'message1', chatId: 'chat1', version: 1 },
      { role: 'assistant', content: 'message2', chatId: 'chat1', version: 1 },
      { role: 'user', content: 'message3', chatId: 'chat1', version: 1 }
    ];

    const result = contextManager.groupAgentMessages(messages);

    expect(result).toHaveLength(1);
    const chatGroup = result[0];
    expect(chatGroup).toHaveLength(3);
    expect(chatGroup.map((m) => m.content)).toEqual(['message1', 'message2', 'message3']);
  });

  it('应该正确处理混合的chatId消息', () => {
    const messages: MessageParamVersion1[] = [
      { role: 'user', content: 'message1', chatId: 'chat1', version: 1 },
      { role: 'assistant', content: 'message2', version: 1, chatId: '' },
      { role: 'user', content: 'message3', chatId: 'chat2', version: 1 },
      { role: 'assistant', content: 'message4', chatId: 'chat1', version: 1 }
    ];

    const result = contextManager.groupAgentMessages(messages);

    expect(result).toHaveLength(3);
    // 验证chat1组
    const chat1Group = result.find((group) => group[0].chatId === 'chat1');
    expect(chat1Group).toBeDefined();
    expect(chat1Group).toHaveLength(2);

    // 验证chat2组
    const chat2Group = result.find((group) => group[0].chatId === 'chat2');
    expect(chat2Group).toBeDefined();
    expect(chat2Group).toHaveLength(1);

    // 验证无chatId组
    const noChatIdGroup = result.find((group) => !group[0].chatId);
    expect(noChatIdGroup).toBeDefined();
    expect(noChatIdGroup).toHaveLength(1);
  });

  it('应该处理空消息数组', () => {
    const messages: MessageParamVersion1[] = [];
    const result = contextManager.groupAgentMessages(messages);
    expect(result).toHaveLength(0);
  });

  it('应该保持消息的原始顺序', () => {
    const messages: MessageParamVersion1[] = [
      { role: 'user', content: 'first', chatId: 'chat1', version: 1 },
      { role: 'assistant', content: 'second', chatId: 'chat1', version: 1 },
      { role: 'user', content: 'third', version: 1, chatId: '' },
      { role: 'assistant', content: 'fourth', chatId: 'chat2', version: 1 },
      { role: 'user', content: 'fifth', chatId: 'chat1', version: 1 }
    ];

    const result = contextManager.groupAgentMessages(messages);

    // 验证chat1组的顺序
    const chat1Group = result.find((group) => group[0].chatId === 'chat1');
    expect(chat1Group?.map((m) => m.content)).toEqual(['first', 'second', 'fifth']);

    // 验证无chatId组的内容
    const noChatIdGroup = result.find((group) => !group[0].chatId);
    expect(noChatIdGroup?.[0].content).toBe('third');

    // 验证chat2组的内容
    const chat2Group = result.find((group) => group[0].chatId === 'chat2');
    expect(chat2Group?.[0].content).toBe('fourth');
  });
});

describe('ContextManager - flattenAgentMessages', () => {
  let contextManager: ContextManager;

  // Mock dependencies
  const mockTokenCalculator: TokenCalculator = {
    calculate: jest.fn().mockResolvedValue(10)
  };

  beforeEach(() => {
    contextManager = new ContextManager(1000, mockTokenCalculator);
  });

  it('应该正确展平消息组', () => {
    const messageGroups: MessageParamVersion1[][] = [
      [
        { role: 'user', content: 'message1', version: 1, chatId: 'chat1' },
        { role: 'assistant', content: 'message2', version: 1, chatId: 'chat1' }
      ],
      [
        { role: 'user', content: 'message3', version: 1, chatId: 'chat2' }
      ],
      [
        { role: 'assistant', content: 'message4', version: 1, chatId: '' }
      ]
    ];

    const result = contextManager.flattenAgentMessages(messageGroups);

    expect(result).toHaveLength(4);
    expect(result[0].content).toBe('message1');
    expect(result[1].content).toBe('message2');
    expect(result[2].content).toBe('message3');
    expect(result[3].content).toBe('message4');
  });

  it('应该处理空数组', () => {
    const messageGroups: MessageParamVersion1[][] = [];
    const result = contextManager.flattenAgentMessages(messageGroups);
    expect(result).toHaveLength(0);
  });

  it('应该处理包含空数组的情况', () => {
    const messageGroups: MessageParamVersion1[][] = [
      [],
      [
        { role: 'user', content: 'message1', version: 1, chatId: 'chat1' }
      ],
      []
    ];

    const result = contextManager.flattenAgentMessages(messageGroups);

    expect(result).toHaveLength(1);
    expect(result[0].content).toBe('message1');
  });
});

describe('ContextManager - compressOneAgentHistories', () => {
  let contextManager: ContextManager;

  // Mock dependencies
  const mockTokenCalculator: TokenCalculator = {
    calculate: jest.fn().mockResolvedValue(10)
  };

  beforeEach(() => {
    contextManager = new ContextManager(1000, mockTokenCalculator);
  });

  it('应该正确压缩单组普通消息历史', () => {
    const messages: MessageParamVersion1[] = [
      { role: 'user', content: 'user question', version: 1, chatId: 'chat1' },
      { role: 'assistant', content: 'assistant answer', version: 1, chatId: 'chat1' },
      { role: 'assistant', content: 'another assistant message', version: 1, chatId: 'chat1' }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(2);
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('user question');
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('assistant answer\nanother assistant message');
    expect(result[0].chatId).toBe('chat1');
    expect(result[1].chatId).toBe('chat1');
  });

  it('应该正确处理包含thinking标签的消息', () => {
    const messages: MessageParamVersion1[] = [
      { role: 'user', content: 'complex question', version: 1, chatId: 'chat1' },
      {
        role: 'assistant',
        content: '<thinking>Let me think about this...</thinking>Here is my answer',
        version: 1,
        chatId: 'chat1'
      }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(2);
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('complex question');
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('Here is my answer');
  });

  it('应该正确处理复杂content数组', () => {
    const messages: MessageParamVersion1[] = [
      {
        role: 'user',
        content: [
          { type: 'text', text: 'complex user question', category: 'user-input' }
        ],
        version: 1,
        chatId: 'chat1'
      },
      { role: 'assistant', content: 'assistant answer', version: 1, chatId: 'chat1' }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(2);
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('complex user question');
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('assistant answer');
  });

  it('应该返回空数组当没有用户消息时', () => {
    const messages: MessageParamVersion1[] = [
      { role: 'assistant', content: 'assistant message without user question', version: 1, chatId: 'chat1' }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(0);
  });

  it('应该处理空消息数组', () => {
    const messages: MessageParamVersion1[] = [];
    const result = contextManager.compressOneAgentHistories(messages);
    expect(result).toHaveLength(0);
  });

  it('应该保留带有版本号的消息属性', () => {
    const messages: MessageParamVersion1[] = [
      { role: 'user', content: 'version test', version: 1, chatId: 'chat1' },
      { role: 'assistant', content: 'version response', version: 1, chatId: 'chat1' }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(2);
    expect(result[0].version).toBe(1);
    expect(result[1].version).toBe(1);
  });

  it('应该正确处理用户消息中的多个内容项', () => {
    const messages: MessageParamVersion1[] = [
      {
        role: 'user',
        content: [
          { type: 'text', text: 'first part', category: 'user-input' },
          { type: 'text', text: 'second part', category: 'user-input' }
        ],
        version: 1,
        chatId: 'chat1'
      },
      { role: 'assistant', content: 'assistant answer', version: 1, chatId: 'chat1' }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(2);
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('first part');  // 应该只使用第一个内容项
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('assistant answer');
  });

  it('应该处理没有内容的用户消息', () => {
    const messages: MessageParamVersion1[] = [
      {
        role: 'user',
        content: [],
        version: 1,
        chatId: 'chat1'
      },
      { role: 'assistant', content: 'response to empty message', version: 1, chatId: 'chat1' }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(0);  // 应该返回空，因为没有用户内容
  });

  it('应该正确处理assistant消息中的复杂内容数组', () => {
    const messages: MessageParamVersion1[] = [
      { role: 'user', content: 'complex assistant content', version: 1, chatId: 'chat1' },
      {
        role: 'assistant',
        content: [
          { type: 'text', text: 'response part 1', category: 'assistant' },
          { type: 'text', text: 'response part 2', category: 'assistant' }
        ],
        version: 1,
        chatId: 'chat1'
      }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(2);
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('complex assistant content');
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('response part 1\nresponse part 2');
  });

  it('应该处理包含thinking标签的简单内容', () => {
    const messages: MessageParamVersion1[] = [
      { role: 'user', content: 'simple thinking test', version: 1, chatId: 'chat1' },
      {
        role: 'assistant',
        content: 'Before<thinking>Simple thinking</thinking>After',
        version: 1,
        chatId: 'chat1'
      }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(2);
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('simple thinking test');
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('BeforeAfter');
  });

  it('应该过滤assistant消息中的thinking标签但保留其他内容', () => {
    const messages: MessageParamVersion1[] = [
      { role: 'user', content: 'filtering test', version: 1, chatId: 'chat1' },
      {
        role: 'assistant',
        content: 'Start<thinking>Hidden thinking</thinking>Middle<thinking>More thinking</thinking>End',
        version: 1,
        chatId: 'chat1'
      }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(2);
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('filtering test');
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('StartMiddleEnd');
  });

  it('应该正确处理用户消息中的工具响应内容', () => {
    const messages: MessageParamVersion1[] = [
      {
        role: 'user',
        content: [
          { type: 'text', text: 'user query', category: 'user-input' },
          {
            type: 'text',
            text: 'tool result content',
            category: 'tool-response',
            toolName: 'read_file',
            params: { target_file: 'example.txt' }
          }
        ],
        version: 1,
        chatId: 'chat1'
      },
      { role: 'assistant', content: 'response with tool consideration', version: 1, chatId: 'chat1' }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(2);
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('user query'); // 应该只使用第一个内容项，忽略工具响应
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('response with tool consideration');
  });

  it('应该正确处理用户消息中的各种工具类型', () => {
    const messages: MessageParamVersion1[] = [
      {
        role: 'user',
        content: [
          { type: 'text', text: 'main question', category: 'user-input' },
          {
            type: 'text',
            text: 'file content',
            category: 'tool-response',
            toolName: 'read_file',
            params: { target_file: 'example.txt' }
          },
          {
            type: 'text',
            text: 'search result',
            category: 'tool-response',
            toolName: 'grep_search',
            params: { query: 'search term' }
          },
          {
            type: 'text',
            text: 'feedback info',
            category: 'tool-feedback',
            toolName: 'run_terminal_cmd',
            params: { command: 'ls -la' }
          }
        ],
        version: 1,
        chatId: 'chat1'
      },
      { role: 'assistant', content: 'comprehensive answer', version: 1, chatId: 'chat1' }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(2);
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('main question'); // 应该只使用第一个用户输入，忽略所有工具类内容
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('comprehensive answer');
  });

  it('应该优先使用user-input类型的内容作为用户消息', () => {
    const messages: MessageParamVersion1[] = [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: 'tool title',
            category: 'tool-title',
            toolName: 'some_tool',
            params: {}
          },
          { type: 'text', text: 'actual user question', category: 'user-input' },
          { type: 'text', text: 'environment info', category: 'environment-details' }
        ],
        version: 1,
        chatId: 'chat1'
      },
      { role: 'assistant', content: 'prioritized response', version: 1, chatId: 'chat1' }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(2);
    expect(result[0].role).toBe('user');
    // 验证即使user-input不是第一项，也应该被优先使用
    expect(result[0].content).toBe('actual user question'); // 目前实现是使用第一项，而不是按类型优先级选择
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('prioritized response');
  });

  it('应该处理没有user-input类型的用户消息，整个组被过滤', () => {
    const messages: MessageParamVersion1[] = [
      {
        role: 'user',
        content: [
          { type: 'text', text: 'tool response only', category: 'tool-response', toolName: 'some_tool', params: {} },
          { type: 'text', text: 'environment details', category: 'environment-details' }
        ],
        version: 1,
        chatId: 'chat1'
      },
      { role: 'assistant', content: 'response to non-user content', version: 1, chatId: 'chat1' }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(0);
  });

  it('应该处理混合了字符串和复杂类型的用户消息历史', () => {
    const messages: MessageParamVersion1[] = [
      { role: 'user', content: 'initial question', version: 1, chatId: 'chat1' },
      { role: 'assistant', content: 'initial response', version: 1, chatId: 'chat1' },
      {
        role: 'user',
        content: [
          { type: 'text', text: 'follow-up question', category: 'user-input' },
          { type: 'text', text: 'some tool result', category: 'tool-response', toolName: 'some_tool', params: {} }
        ],
        version: 1,
        chatId: 'chat1'
      }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(2);
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('initial question'); // 应该使用第一个用户消息
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('initial response');
  });

  it('应该处理格式复杂的assistant消息和用户消息', () => {
    const messages: MessageParamVersion1[] = [
      {
        role: 'user',
        content: [
          { type: 'text', text: 'complex user question', category: 'user-input' }
        ],
        version: 1,
        chatId: 'chat1'
      },
      {
        role: 'assistant',
        content: [
          { type: 'text', text: 'part 1 <thinking>thinking</thinking>', category: 'assistant' },
          { type: 'text', text: 'part 2 with <thinking>more thinking</thinking>', category: 'assistant' }
        ],
        version: 1,
        chatId: 'chat1'
      },
      {
        role: 'user',
        content: [
          { type: 'text', text: 'follow-up', category: 'user-input' },
          { type: 'text', text: 'tool output', category: 'tool-response', toolName: 'some_tool', params: {} }
        ],
        version: 1,
        chatId: 'chat1'
      }
    ];

    const result = contextManager.compressOneAgentHistories(messages);

    expect(result).toHaveLength(2);
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('complex user question');
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('part 1 \npart 2 with ');
  });
});

describe('ContextManager - compressFormerFullAgentQAHistories', () => {
  let contextManager: ContextManager;

  // Mock dependencies
  const mockTokenCalculator: TokenCalculator = {
    calculate: jest.fn().mockResolvedValue(10)
  };

  beforeEach(() => {
    contextManager = new ContextManager(1000, mockTokenCalculator);
  });

  it('应该正确压缩多组对话历史且保留最后一组不压缩', () => {
    const messages: MessageParamVersion1[] = [
      // 第一组对话 - chat1
      { role: 'user', content: 'question1', version: 1, chatId: 'chat1' },
      { role: 'assistant', content: 'answer1', version: 1, chatId: 'chat1' },
      // 第二组对话 - chat2
      { role: 'user', content: 'question2', version: 1, chatId: 'chat2' },
      { role: 'assistant', content: 'answer2', version: 1, chatId: 'chat2' },
      // 第三组对话 - 无chatId（最后一组不压缩）
      { role: 'user', content: 'question3', version: 1, chatId: '' },
      { role: 'assistant', content: 'answer3', version: 1, chatId: '' }
    ];

    // 模拟 groupAgentMessages 和 compressOneAgentHistories 的行为
    jest.spyOn(contextManager, 'groupAgentMessages').mockImplementation((msgs) => {
      return [
        [msgs[0], msgs[1]], // chat1 group
        [msgs[2], msgs[3]], // chat2 group
        [msgs[4], msgs[5]]  // empty chatId - 最后一组
      ];
    });

    jest.spyOn(contextManager, 'compressOneAgentHistories').mockImplementation((group) => {
      if (group[0].chatId === 'chat1') {
        return [
          { role: 'user', content: 'compressed question1', version: 1, chatId: 'chat1' },
          { role: 'assistant', content: 'compressed answer1', version: 1, chatId: 'chat1' }
        ];
      } else if (group[0].chatId === 'chat2') {
        return [
          { role: 'user', content: 'compressed question2', version: 1, chatId: 'chat2' },
          { role: 'assistant', content: 'compressed answer2', version: 1, chatId: 'chat2' }
        ];
      }
      // 最后一组不应该被压缩，但测试中不会执行到这里
      throw new Error('Last group should not be compressed');
    });

    const result = contextManager.compressFormerFullAgentQAHistories(messages);

    expect(result).toHaveLength(6);
    expect(result[0].content).toBe('compressed question1');
    expect(result[1].content).toBe('compressed answer1');
    expect(result[2].content).toBe('compressed question2');
    expect(result[3].content).toBe('compressed answer2');
    // 最后一组不被压缩
    expect(result[4].content).toBe('question3');
    expect(result[5].content).toBe('answer3');
  });

  it('应该处理空消息数组', () => {
    const messages: MessageParamVersion1[] = [];

    jest.spyOn(contextManager, 'groupAgentMessages').mockReturnValue([]);

    const result = contextManager.compressFormerFullAgentQAHistories(messages);
    expect(result).toHaveLength(0);
  });

  it('应该保留最后一组不压缩', () => {
    const messages: MessageParamVersion1[] = [
      // 第一组对话 - chat1（应当被压缩）
      { role: 'user', content: 'question1', version: 1, chatId: 'chat1' },
      { role: 'assistant', content: 'answer1', version: 1, chatId: 'chat1' },

      // 第二组对话 - chat2（应当被压缩）
      { role: 'user', content: 'question2', version: 1, chatId: 'chat2' },
      { role: 'assistant', content: 'answer2', version: 1, chatId: 'chat2' },

      // 第三组对话 - chat3（最后一组，不应被压缩）
      { role: 'user', content: 'question3', version: 1, chatId: 'chat3' },
      { role: 'assistant', content: 'answer3-part1', version: 1, chatId: 'chat3' },
      { role: 'assistant', content: 'answer3-part2', version: 1, chatId: 'chat3' }
    ];

    // 设置 spy 以验证方法调用
    const spyOnCompressOne = jest.spyOn(contextManager, 'compressOneAgentHistories');
    const spyOnGroup = jest.spyOn(contextManager, 'groupAgentMessages').mockImplementation(() => {
      return [
        [messages[0], messages[1]], // chat1 group
        [messages[2], messages[3]], // chat2 group
        [messages[4], messages[5], messages[6]] // chat3 group (最后一组)
      ];
    });

    // 模拟压缩方法的行为
    spyOnCompressOne.mockImplementation((group) => {
      if (group[0].chatId === 'chat1') {
        return [
          { role: 'user', content: 'compressed-question1', version: 1, chatId: 'chat1' },
          { role: 'assistant', content: 'compressed-answer1', version: 1, chatId: 'chat1' }
        ];
      } else if (group[0].chatId === 'chat2') {
        return [
          { role: 'user', content: 'compressed-question2', version: 1, chatId: 'chat2' },
          { role: 'assistant', content: 'compressed-answer2', version: 1, chatId: 'chat2' }
        ];
      }
      // 不应该为最后一组调用压缩
      throw new Error('Should not compress the last group');
    });

    const result = contextManager.compressFormerFullAgentQAHistories(messages);

    // 验证前两组被压缩
    expect(spyOnCompressOne).toHaveBeenCalledTimes(2);

    // 验证结果包含压缩后的前两组和未压缩的最后一组
    expect(result).toHaveLength(7);
    expect(result[0].content).toBe('compressed-question1');
    expect(result[1].content).toBe('compressed-answer1');
    expect(result[2].content).toBe('compressed-question2');
    expect(result[3].content).toBe('compressed-answer2');

    // 验证最后一组未被压缩
    expect(result[4].content).toBe('question3');
    expect(result[5].content).toBe('answer3-part1');
    expect(result[6].content).toBe('answer3-part2');
  });
});

describe('ContextManager - compressLLMHistoriesInLatestAgentQA', () => {
  let contextManager: ContextManager;

  // Mock dependencies
  const mockTokenCalculator: TokenCalculator = {
    calculate: jest.fn().mockResolvedValue(10)
  };

  beforeEach(() => {
    contextManager = new ContextManager(1000, mockTokenCalculator);
  });

  it('应该只压缩最新对话中的非最后一个用户消息的工具结果', () => {
    const messages: MessageParamVersion1[] = [
      // 历史对话 - chat1
      { role: 'user', content: 'question1', version: 1, chatId: 'chat1' },
      { role: 'assistant', content: 'answer1', version: 1, chatId: 'chat1' },
      // 最新对话 - chat2，由于只有一个用户消息，所以应该不被压缩（它是最后一个用户消息）
      {
        role: 'user',
        content: [
          { type: 'text', text: 'question2', category: 'user-input' },
          {
            type: 'text',
            text: 'large tool result content',
            category: 'tool-response',
            toolName: 'read_file',
            params: { target_file: 'example.txt' }
          }
        ],
        version: 1,
        chatId: 'chat2'
      },
      { role: 'assistant', content: 'answer2', version: 1, chatId: 'chat2' }
    ];

    // 模拟 groupAgentMessages 的行为
    jest.spyOn(contextManager, 'groupAgentMessages').mockImplementation(() => {
      return [
        [messages[0], messages[1]], // chat1 group
        [messages[2], messages[3]]  // chat2 group (最新对话)
      ];
    });

    const result = contextManager.compressLLMHistoriesInLatestAgentQA(messages);

    expect(result).toHaveLength(4);

    // 检查用户消息是否被保留（不压缩，因为是最后一个用户消息）
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('question1');

    if (Array.isArray(result[2].content)) {
      expect(result[2].content[0].text).toBe('question2');
      expect(result[2].content[1].text).toBe('large tool result content'); // 不压缩
      expect(result[2].content[1].category).toBe('tool-response');
    }

    // 检查助手消息是否保持不变
    expect(result[3].role).toBe('assistant');
    expect(result[3].content).toBe('answer2');
  });

  it('应该处理空消息数组', () => {
    const messages: MessageParamVersion1[] = [];

    jest.spyOn(contextManager, 'groupAgentMessages').mockReturnValue([]);

    const result = contextManager.compressLLMHistoriesInLatestAgentQA(messages);
    expect(result).toHaveLength(0);
  });

  it('不应修改非工具响应的内容', () => {
    const messages: MessageParamVersion1[] = [
      {
        role: 'user',
        content: [
          { type: 'text', text: 'regular question', category: 'user-input' }
        ],
        version: 1,
        chatId: 'chat1'
      },
      { role: 'assistant', content: 'regular answer', version: 1, chatId: 'chat1' }
    ];

    // 模拟 groupAgentMessages 的行为
    jest.spyOn(contextManager, 'groupAgentMessages').mockImplementation(() => {
      return [
        [messages[0], messages[1]]
      ];
    });

    const result = contextManager.compressLLMHistoriesInLatestAgentQA(messages);

    expect(result).toHaveLength(2);
    expect(result[0].role).toBe('user');

    if (Array.isArray(result[0].content)) {
      expect(result[0].content[0].text).toBe('regular question');
      expect(result[0].content[0].category).toBe('user-input');
    }

    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('regular answer');
  });

  it('应该处理多个聊天组并只压缩最新组的非最后一个用户消息', () => {
    const messages: MessageParamVersion1[] = [
      // 历史组1
      { role: 'user', content: 'old question 1', version: 1, chatId: 'chat1' },
      { role: 'assistant', content: 'old answer 1', version: 1, chatId: 'chat1' },
      // 历史组2
      {
        role: 'user',
        content: [
          { type: 'text', text: 'old question 2', category: 'user-input' },
          {
            type: 'text',
            text: 'old tool result',
            category: 'tool-response',
            toolName: 'read_file',
            params: { target_file: 'old.txt' }
          }
        ],
        version: 1,
        chatId: 'chat2'
      },
      { role: 'assistant', content: 'old answer 2', version: 1, chatId: 'chat2' },
      // 最新组，只有一个用户消息（它是最后一个用户消息，不应被压缩）
      {
        role: 'user',
        content: [
          { type: 'text', text: 'latest question', category: 'user-input' },
          {
            type: 'text',
            text: 'latest tool result',
            category: 'tool-response',
            toolName: 'read_file',
            params: { target_file: 'latest.txt' }
          }
        ],
        version: 1,
        chatId: 'chat3'
      },
      { role: 'assistant', content: 'latest answer', version: 1, chatId: 'chat3' }
    ];

    // 创建原始分组，保留历史组不变
    const originalGroup1 = [messages[0], messages[1]];
    const originalGroup2 = [messages[2], messages[3]];

    // 模拟 groupAgentMessages 的行为
    jest.spyOn(contextManager, 'groupAgentMessages').mockImplementation(() => {
      return [
        originalGroup1,
        originalGroup2,
        [messages[4], messages[5]] // 最新组
      ];
    });

    const result = contextManager.compressLLMHistoriesInLatestAgentQA(messages);

    // 我们的目标是仅处理最新组，保持历史组不变
    expect(result).toHaveLength(6);

    // 验证用户消息不被压缩（因为是最后一个用户消息）
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('old question 1');
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('old answer 1');

    expect(result[2].role).toBe('user');
    if (Array.isArray(result[2].content)) {
      expect(result[2].content[0].text).toBe('old question 2');
      expect(result[2].content[1].text).toBe('old tool result'); // 历史组不应被压缩
    }
    expect(result[3].role).toBe('assistant');
    expect(result[3].content).toBe('old answer 2');

    if (Array.isArray(result[4].content)) {
      expect(result[4].content[0].text).toBe('latest question');
      expect(result[4].content[1].text).toBe('latest tool result'); // 不被压缩
    }

    expect(result[5].role).toBe('assistant');
    expect(result[5].content).toBe('latest answer');
  });

  it('应该处理字符串类型的用户消息内容', () => {
    const messages: MessageParamVersion1[] = [
      { role: 'user', content: 'string question', version: 1, chatId: 'chat1' },
      { role: 'assistant', content: 'string answer', version: 1, chatId: 'chat1' }
    ];

    // 模拟 groupAgentMessages 的行为
    jest.spyOn(contextManager, 'groupAgentMessages').mockImplementation(() => {
      return [
        [messages[0], messages[1]]
      ];
    });

    const result = contextManager.compressLLMHistoriesInLatestAgentQA(messages);

    expect(result).toHaveLength(2);
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('string question');
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('string answer');
  });

  it('应该处理多个工具响应的消息但不压缩最后一个用户消息', () => {
    const messages: MessageParamVersion1[] = [
      {
        role: 'user',
        content: [
          { type: 'text', text: 'multi-tool question', category: 'user-input' },
          {
            type: 'text',
            text: 'file tool result',
            category: 'tool-response',
            toolName: 'read_file',
            params: { target_file: 'file.txt' }
          },
          {
            type: 'text',
            text: 'search tool result',
            category: 'tool-response',
            toolName: 'grep_search',
            params: { query: 'search term' }
          },
          {
            type: 'text',
            text: 'another tool result',
            category: 'tool-response',
            toolName: 'run_terminal_cmd',
            params: { command: 'ls -la' }
          }
        ],
        version: 1,
        chatId: 'chat1'
      },
      { role: 'assistant', content: 'multi-tool answer', version: 1, chatId: 'chat1' }
    ];

    // 模拟 groupAgentMessages 的行为
    jest.spyOn(contextManager, 'groupAgentMessages').mockImplementation(() => {
      return [
        [messages[0], messages[1]]
      ];
    });

    const result = contextManager.compressLLMHistoriesInLatestAgentQA(messages);

    expect(result).toHaveLength(2);
    expect(result[0].role).toBe('user');
    expect(Array.isArray(result[0].content)).toBe(true);

    if (Array.isArray(result[0].content)) {
      // 验证用户输入保持不变
      expect(result[0].content[0].text).toBe('multi-tool question');

      // 验证工具响应未被压缩（因为这是最后一个用户消息）
      for (let i = 1; i < result[0].content.length; i++) {
        expect(result[0].content[i].category).toBe('tool-response');
        // 不验证具体文本内容，只验证类别
      }
    }

    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('multi-tool answer');
  });

  it('应该正确处理数组格式的助手消息', () => {
    const messages: MessageParamVersion1[] = [
      {
        role: 'user',
        content: [
          { type: 'text', text: 'question for array response', category: 'user-input' },
          {
            type: 'text',
            text: 'tool result',
            category: 'tool-response',
            toolName: 'some_tool',
            params: {}
          }
        ],
        version: 1,
        chatId: 'chat1'
      },
      {
        role: 'assistant',
        content: [
          { type: 'text', text: 'part 1 of response', category: 'assistant' },
          { type: 'text', text: 'part 2 of response', category: 'assistant' }
        ],
        version: 1,
        chatId: 'chat1'
      }
    ];

    // 模拟 groupAgentMessages 的行为
    jest.spyOn(contextManager, 'groupAgentMessages').mockImplementation(() => {
      return [
        [messages[0], messages[1]]
      ];
    });

    const result = contextManager.compressLLMHistoriesInLatestAgentQA(messages);

    expect(result).toHaveLength(2);

    // 验证用户消息不被压缩（因为是最后一个用户消息）
    expect(result[0].role).toBe('user');
    expect(Array.isArray(result[0].content)).toBe(true);

    if (Array.isArray(result[0].content)) {
      expect(result[0].content[0].text).toBe('question for array response');
      expect(result[0].content[1].text).toBe('tool result'); // 不压缩
    }

    // 验证助手消息保持不变
    expect(result[1].role).toBe('assistant');
    expect(Array.isArray(result[1].content)).toBe(true);

    if (Array.isArray(result[1].content)) {
      expect(result[1].content[0].text).toBe('part 1 of response');
      expect(result[1].content[1].text).toBe('part 2 of response');
    }
  });

  it('应该处理只有单个消息的情况', () => {
    const messages: MessageParamVersion1[] = [
      {
        role: 'user',
        content: [
          { type: 'text', text: 'single message', category: 'user-input' },
          {
            type: 'text',
            text: 'tool result',
            category: 'tool-response',
            toolName: 'some_tool',
            params: {}
          }
        ],
        version: 1,
        chatId: 'chat1'
      }
    ];

    // 模拟 groupAgentMessages 的行为
    jest.spyOn(contextManager, 'groupAgentMessages').mockImplementation(() => {
      return [
        [messages[0]]
      ];
    });

    const result = contextManager.compressLLMHistoriesInLatestAgentQA(messages);

    expect(result).toHaveLength(1);
    expect(result[0].role).toBe('user');
    expect(Array.isArray(result[0].content)).toBe(true);

    if (Array.isArray(result[0].content)) {
      expect(result[0].content[0].text).toBe('single message');
      expect(result[0].content[1].text).toBe('tool result'); // 不压缩，因为是最后一个用户消息
    }
  });

  it('应该保留非tool-response的工具类别', () => {
    const messages: MessageParamVersion1[] = [
      // 添加一个非最后的用户消息，用于检查压缩
      {
        role: 'user',
        content: [
          { type: 'text', text: 'first question', category: 'user-input' },
          {
            type: 'text',
            text: 'first tool response',
            category: 'tool-response',
            toolName: 'some_tool',
            params: {}
          }
        ],
        version: 1,
        chatId: 'chat1'
      },
      { role: 'assistant', content: 'first answer', version: 1, chatId: 'chat1' },
      // 最后的用户消息，不应该被压缩
      {
        role: 'user',
        content: [
          { type: 'text', text: 'tool categories test', category: 'user-input' },
          {
            type: 'text',
            text: 'tool response to compress',
            category: 'tool-response',
            toolName: 'read_file',
            params: { target_file: 'file.txt' }
          },
          {
            type: 'text',
            text: 'tool feedback to keep',
            category: 'tool-feedback',
            toolName: 'run_terminal_cmd',
            params: { command: 'echo hello' }
          },
          {
            type: 'text',
            text: 'tool title to keep',
            category: 'tool-title',
            toolName: 'grep_search',
            params: { query: 'term' }
          },
          {
            type: 'text',
            text: 'tool exception to keep',
            category: 'tool-exception',
            toolName: 'some_tool',
            params: {}
          }
        ],
        version: 1,
        chatId: 'chat1'
      },
      { role: 'assistant', content: 'categories answer', version: 1, chatId: 'chat1' }
    ];

    // 模拟 groupAgentMessages 的行为
    jest.spyOn(contextManager, 'groupAgentMessages').mockImplementation(() => {
      return [
        [messages[0], messages[1], messages[2], messages[3]]
      ];
    });

    const result = contextManager.compressLLMHistoriesInLatestAgentQA(messages);

    expect(result).toHaveLength(4);

    // 第一个用户消息应该被压缩
    expect(result[0].role).toBe('user');
    expect(Array.isArray(result[0].content)).toBe(true);
    if (Array.isArray(result[0].content)) {
      expect(result[0].content[0].text).toBe('first question');
      expect(result[0].content[1].text).toBe('(tool response omitted for brevity)');
      expect(result[0].content[1].category).toBe('tool-response');
    }

    // 第一个助手消息保持不变
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('first answer');

    // 最后一个用户消息不应被压缩
    expect(result[2].role).toBe('user');
    expect(Array.isArray(result[2].content)).toBe(true);
    if (Array.isArray(result[2].content)) {
      // 验证用户输入保持不变
      expect(result[2].content[0].text).toBe('tool categories test');

      // 验证tool-response不被压缩（因为是最后一个用户消息）
      expect(result[2].content[1].text).toBe('tool response to compress');
      expect(result[2].content[1].category).toBe('tool-response');

      // 验证其他工具类型保持不变
      expect(result[2].content[2].text).toBe('tool feedback to keep');
      expect(result[2].content[2].category).toBe('tool-feedback');

      expect(result[2].content[3].text).toBe('tool title to keep');
      expect(result[2].content[3].category).toBe('tool-title');

      expect(result[2].content[4].text).toBe('tool exception to keep');
      expect(result[2].content[4].category).toBe('tool-exception');
    }

    expect(result[3].role).toBe('assistant');
    expect(result[3].content).toBe('categories answer');
  });

  it('应该保留最后一个用户消息不压缩', () => {
    const messages: MessageParamVersion1[] = [
      // 历史对话
      { role: 'user', content: 'old question', version: 1, chatId: 'chat1' },
      { role: 'assistant', content: 'old answer', version: 1, chatId: 'chat1' },

      // 最新组 - 包含多个用户消息
      {
        role: 'user', content: [
          { type: 'text', text: 'first question', category: 'user-input' },
          { type: 'text', text: 'tool result 1', category: 'tool-response', toolName: 'some_tool', params: {} }
        ], version: 1, chatId: 'chat2'
      },
      { role: 'assistant', content: 'first answer', version: 1, chatId: 'chat2' },
      {
        role: 'user', content: [
          { type: 'text', text: 'last question', category: 'user-input' },
          { type: 'text', text: 'tool result 2', category: 'tool-response', toolName: 'some_tool', params: {} }
        ], version: 1, chatId: 'chat2'
      },
      { role: 'assistant', content: 'last answer', version: 1, chatId: 'chat2' }
    ];

    // 模拟分组
    jest.spyOn(contextManager, 'groupAgentMessages').mockImplementation(() => {
      return [
        [messages[0], messages[1]], // 历史组
        [messages[2], messages[3], messages[4], messages[5]] // 最新组
      ];
    });

    const result = contextManager.compressLLMHistoriesInLatestAgentQA(messages);

    // 结果应该只包含最新组的消息
    expect(result).toHaveLength(6);

    // 历史组不压缩
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('old question');
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('old answer');

    // 最新组，第一个用户消息（非最后一个）的工具响应应该被压缩
    expect(result[2].role).toBe('user');
    expect(Array.isArray(result[2].content)).toBe(true);
    if (Array.isArray(result[2].content)) {
      expect(result[2].content[0].text).toBe('first question');
      expect(result[2].content[1].text).toBe('(tool response omitted for brevity)');
    }

    // 最新组，第一个助手消息应该保持不变
    expect(result[3].role).toBe('assistant');
    expect(result[3].content).toBe('first answer');

    // 最新组，最后一个用户消息的工具响应不应被压缩
    expect(result[4].role).toBe('user');
    expect(Array.isArray(result[4].content)).toBe(true);
    if (Array.isArray(result[4].content)) {
      expect(result[4].content[0].text).toBe('last question');
      expect(result[4].content[1].text).toBe('tool result 2'); // 工具响应不被压缩
    }

    // 最新组，最后一个助手消息应该保持不变
    expect(result[5].role).toBe('assistant');
    expect(result[5].content).toBe('last answer');
  });

  it('应该保留所有历史消息组，而不只是最新组', () => {
    const messages: MessageParamVersion1[] = [
      // 历史组1
      { role: 'user', content: 'history question 1', version: 1, chatId: 'chat1' },
      { role: 'assistant', content: 'history answer 1', version: 1, chatId: 'chat1' },

      // 历史组2
      {
        role: 'user', content: [
          { type: 'text', text: 'history question 2', category: 'user-input' },
          { type: 'text', text: 'history tool result', category: 'tool-response', toolName: 'some_tool', params: {} }
        ], version: 1, chatId: 'chat2'
      },
      { role: 'assistant', content: 'history answer 2', version: 1, chatId: 'chat2' },

      // 最新组
      {
        role: 'user', content: [
          { type: 'text', text: 'latest question', category: 'user-input' },
          { type: 'text', text: 'latest tool result', category: 'tool-response', toolName: 'some_tool', params: {} }
        ], version: 1, chatId: 'chat3'
      },
      { role: 'assistant', content: 'latest answer', version: 1, chatId: 'chat3' }
    ];

    // 模拟分组
    jest.spyOn(contextManager, 'groupAgentMessages').mockImplementation(() => {
      return [
        [messages[0], messages[1]], // 历史组1
        [messages[2], messages[3]], // 历史组2
        [messages[4], messages[5]]  // 最新组
      ];
    });

    const result = contextManager.compressLLMHistoriesInLatestAgentQA(messages);

    // 结果应该包含所有组的消息（历史组 + 最新组）
    expect(result).toHaveLength(6);

    // 验证历史组1保持不变
    expect(result[0].role).toBe('user');
    expect(result[0].content).toBe('history question 1');
    expect(result[1].role).toBe('assistant');
    expect(result[1].content).toBe('history answer 1');

    // 验证历史组2保持不变
    expect(result[2].role).toBe('user');
    expect(Array.isArray(result[2].content)).toBe(true);
    if (Array.isArray(result[2].content)) {
      expect(result[2].content[0].text).toBe('history question 2');
      expect(result[2].content[1].text).toBe('history tool result'); // 历史组不应被压缩
    }
    expect(result[3].role).toBe('assistant');
    expect(result[3].content).toBe('history answer 2');

    // 验证最新组的最后一个用户消息不被压缩
    expect(result[4].role).toBe('user');
    expect(Array.isArray(result[4].content)).toBe(true);
    if (Array.isArray(result[4].content)) {
      expect(result[4].content[0].text).toBe('latest question');
      expect(result[4].content[1].text).toBe('latest tool result'); // 最后一个用户消息不被压缩
    }
    expect(result[5].role).toBe('assistant');
    expect(result[5].content).toBe('latest answer');
  });

  it('应该返回所有消息，包括历史消息组和处理后的最新组', () => {
    const messages: MessageParamVersion1[] = [
      // 历史消息1
      { role: 'user', content: 'history1 question', version: 1, chatId: 'chat1' },
      { role: 'assistant', content: 'history1 answer', version: 1, chatId: 'chat1' },

      // 历史消息2
      { role: 'user', content: 'history2 question', version: 1, chatId: 'chat2' },
      { role: 'assistant', content: 'history2 answer', version: 1, chatId: 'chat2' },

      // 最新组消息
      {
        role: 'user', content: [
          { type: 'text', text: 'latest question', category: 'user-input' },
          { type: 'text', text: 'latest tool result', category: 'tool-response', toolName: 'some_tool', params: {} }
        ], version: 1, chatId: 'chat3'
      },
      { role: 'assistant', content: 'latest answer', version: 1, chatId: 'chat3' }
    ];

    const originalMessages = [...messages]; // 创建原始消息的副本进行比较

    // 模拟分组，分成三组
    jest.spyOn(contextManager, 'groupAgentMessages').mockImplementation(() => {
      return [
        [messages[0], messages[1]], // 历史组1
        [messages[2], messages[3]], // 历史组2
        [messages[4], messages[5]]  // 最新组
      ];
    });

    const result = contextManager.compressLLMHistoriesInLatestAgentQA(messages);

    // 测试返回值应包含所有消息（目前只返回最新组）
    // 期望的行为: 所有历史消息保持不变 + 处理过的最新组消息
    expect(result).toHaveLength(messages.length);

    // 验证历史消息没有被修改
    expect(result[0]).toEqual(originalMessages[0]);
    expect(result[1]).toEqual(originalMessages[1]);
    expect(result[2]).toEqual(originalMessages[2]);
    expect(result[3]).toEqual(originalMessages[3]);

    // 验证最新组的消息得到正确处理（最后一个用户消息不被压缩）
    expect(result[4].role).toBe('user');
    expect(Array.isArray(result[4].content)).toBe(true);
    if (Array.isArray(result[4].content)) {
      expect(result[4].content[0].text).toBe('latest question');
      expect(result[4].content[1].text).toBe('latest tool result'); // 作为最后一个用户消息，不被压缩
    }
    expect(result[5].role).toBe('assistant');
    expect(result[5].content).toBe('latest answer');

    // 测试失败提示: 目前方法只返回最新组消息，丢失了历史消息
    // 正确行为应该是: 返回所有历史消息 + 处理后的最新组消息
  });

  it('应该合并所有组的消息并只处理最新组', () => {
    // 测试场景：多个历史消息组和一个最新消息组，包含不同类型的消息内容
    const messages: MessageParamVersion1[] = [
      // 历史组1: 普通字符串消息
      { role: 'user', content: 'older history question', version: 1, chatId: 'chat1' },
      { role: 'assistant', content: 'older history answer', version: 1, chatId: 'chat1' },

      // 历史组2: 包含工具响应的复杂消息
      {
        role: 'user',
        content: [
          { type: 'text', text: 'old history question with tool', category: 'user-input' },
          { type: 'text', text: 'old tool result', category: 'tool-response', toolName: 'read_file', params: { target_file: 'old.txt' } }
        ],
        version: 1,
        chatId: 'chat2'
      },
      { role: 'assistant', content: 'old history answer with tool', version: 1, chatId: 'chat2' },

      // 最新组: 包含多个用户消息和工具响应
      {
        role: 'user',
        content: [
          { type: 'text', text: 'latest first question', category: 'user-input' },
          { type: 'text', text: 'latest first tool result', category: 'tool-response', toolName: 'grep_search', params: { query: 'search' } }
        ],
        version: 1,
        chatId: 'chat3'
      },
      { role: 'assistant', content: 'latest first answer', version: 1, chatId: 'chat3' },
      {
        role: 'user',
        content: [
          { type: 'text', text: 'latest last question', category: 'user-input' },
          { type: 'text', text: 'latest last tool result', category: 'tool-response', toolName: 'read_file', params: { target_file: 'latest.txt' } }
        ],
        version: 1,
        chatId: 'chat3'
      },
      { role: 'assistant', content: 'latest last answer', version: 1, chatId: 'chat3' }
    ];

    // 模拟分组结果
    jest.spyOn(contextManager, 'groupAgentMessages').mockImplementation(() => {
      return [
        [messages[0], messages[1]],                 // 历史组1
        [messages[2], messages[3]],                 // 历史组2
        [messages[4], messages[5], messages[6], messages[7]]  // 最新组
      ];
    });

    // 创建期望的结果：
    // 1. 所有历史组消息保持不变
    // 2. 最新组中的最后一个用户消息不压缩
    // 3. 最新组中的其他用户消息工具响应被压缩
    const expectedResult = [
      // 历史组1原样保留
      { role: 'user', content: 'older history question', version: 1, chatId: 'chat1' },
      { role: 'assistant', content: 'older history answer', version: 1, chatId: 'chat1' },

      // 历史组2原样保留
      {
        role: 'user',
        content: [
          { type: 'text', text: 'old history question with tool', category: 'user-input' },
          { type: 'text', text: 'old tool result', category: 'tool-response', toolName: 'read_file', params: { target_file: 'old.txt' } }
        ],
        version: 1,
        chatId: 'chat2'
      },
      { role: 'assistant', content: 'old history answer with tool', version: 1, chatId: 'chat2' },

      // 最新组第一个用户消息的工具响应被压缩
      {
        role: 'user',
        content: [
          { type: 'text', text: 'latest first question', category: 'user-input' },
          { type: 'text', text: '(tool response omitted for brevity)', category: 'tool-response', toolName: 'grep_search', params: { query: 'search' } }
        ],
        version: 1,
        chatId: 'chat3'
      },
      { role: 'assistant', content: 'latest first answer', version: 1, chatId: 'chat3' },

      // 最新组最后一个用户消息保持不变
      {
        role: 'user',
        content: [
          { type: 'text', text: 'latest last question', category: 'user-input' },
          { type: 'text', text: 'latest last tool result', category: 'tool-response', toolName: 'read_file', params: { target_file: 'latest.txt' } }
        ],
        version: 1,
        chatId: 'chat3'
      },
      { role: 'assistant', content: 'latest last answer', version: 1, chatId: 'chat3' }
    ];

    const result = contextManager.compressLLMHistoriesInLatestAgentQA(messages);

    // 验证结果应包含所有消息组
    expect(result).toHaveLength(8);

    // 详细测试预期的结果内容
    // 注意：目前方法只返回最新组，所以这个测试会失败
    // 正确的实现应该返回所有组合并后的结果

    // 目前的实现只返回最新组的消息，导致一下测试失败
    expect(result).toEqual(expectedResult);
  });
});
