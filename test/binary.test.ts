import { IProtocol, ToIdeProtocol } from '../src/protocol/index.js';
import { IdeCommonMessage, IMessenger } from '../src/protocol/messenger';
import fs from 'fs';
import { ChildProcessWithoutNullStreams, execSync, spawn } from 'node:child_process';
import path from 'path';
import { CoreBinaryMessenger, CoreBinaryTcpMessenger } from '../src/entry/IpcMessenger';

// jest.setTimeout(100_000);
class TestMessenger<T extends ToIdeProtocol, U extends IProtocol>
  extends CoreBinaryMessenger<T, U>
  implements IMessenger<T, U>
{
  constructor(subprocess: ChildProcessWithoutNullStreams) {
    super(subprocess);
  }
  getCommonMessage(): IdeCommonMessage {
    return {
      version: '9.0.1',
      platform: 'vscode',
      cwd: '/Users/<USER>/kwaipilot',
      pluginVersion: '19.0.5',
      repo: {
        git_url: 'https://github.com/kwaipilot/kwaipilot',
        dir_path: '/Users/<USER>/kwaipilot',
        commit: '1234567890'
      }
    };
  }
}

const USE_TCP = false;
function autodetectPlatformAndArch() {
  const platform = {
    aix: 'linux',
    darwin: 'darwin',
    freebsd: 'linux',
    linux: 'linux',
    openbsd: 'linux',
    sunos: 'linux',
    win32: 'win32',
    android: 'linux',
    cygwin: 'win32',
    netbsd: 'linux',
    haiku: 'linux'
  }[process.platform];
  const arch = {
    arm: 'arm64',
    arm64: 'arm64',
    ia32: 'x64',
    loong64: 'arm64',
    mips: 'arm64',
    mipsel: 'arm64',
    ppc: 'x64',
    ppc64: 'x64',
    riscv64: 'arm64',
    s390: 'x64',
    s390x: 'x64',
    x64: 'x64'
  }[process.arch];
  return [platform, arch];
}

const KWAIPILOT_GLOBAL_DIR = path.join(__dirname, '..', '.kwaipilot');
if (fs.existsSync(KWAIPILOT_GLOBAL_DIR)) {
  fs.rmSync(KWAIPILOT_GLOBAL_DIR, { recursive: true, force: true });
}
fs.mkdirSync(KWAIPILOT_GLOBAL_DIR);

describe('Test Suite', () => {
  let messenger: IMessenger<ToIdeProtocol, IProtocol>;
  let subprocess: ChildProcessWithoutNullStreams;

  beforeAll(async () => {
    const [platform, arch] = autodetectPlatformAndArch();
    const binaryDir = path.join(__dirname, '..', 'dist', 'bin', `${platform}-${arch}`);
    const exe = platform === 'win32' ? '.exe' : '';
    const binaryPath = path.join(binaryDir, `kwaipilot-binary${exe}`);
    const expectedItems = [
      `kwaipilot-binary${exe}`,
      // `esbuild${exe}`,
      'index.node',
      'package.json',
      'build/Release/node_sqlite3.node'
    ];
    expectedItems.forEach((item) => {
      expect(fs.existsSync(path.join(binaryDir, item))).toBe(true);
    });

    // Set execute permissions and remove quarantine attribute if on macOS
    if (platform !== 'win32') {
      try {
        fs.chmodSync(binaryPath, 0o755);
        console.log('Execute permissions set for the binary');

        if (platform === 'darwin') {
          const indexNodePath = path.join(binaryDir, 'index.node');
          const filesToUnquarantine = [binaryPath, indexNodePath];

          for (const file of filesToUnquarantine) {
            try {
              execSync(`xattr -d com.apple.quarantine "${file}"`, {
                stdio: 'ignore'
              });
              console.log(`Quarantine attribute removed from ${path.basename(file)}`);
            } catch (error) {
              console.warn(`Failed to remove quarantine attribute from ${path.basename(file)}:`, error);
            }
          }
        }
      } catch (error) {
        console.error('Error setting permissions or removing quarantine:', error);
      }
    }

    if (USE_TCP) {
      messenger = new CoreBinaryTcpMessenger<ToIdeProtocol, IProtocol>();
    } else {
      try {
        subprocess = spawn(binaryPath, {
          env: { ...process.env, KWAIPILOT_GLOBAL_DIR, NODE_ENV: 'production' }
        });
        console.log('Successfully spawned subprocess');
      } catch (error) {
        console.error('Error spawning subprocess:', error);
        throw error;
      }
      messenger = new TestMessenger<ToIdeProtocol, IProtocol>(subprocess);
    }

    const testDir = path.join(__dirname, '..', '.test');
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
  });

  afterAll(async () => {
    // Wait for the subprocess to exit
    if (USE_TCP) {
      (messenger as CoreBinaryTcpMessenger<ToIdeProtocol, IProtocol>).close();
    } else {
      subprocess.kill();
      await new Promise((resolve) => subprocess.on('close', resolve));
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }
  });

  it('健康检查接口', async () => {
    const resp = await messenger.request('state/agentState', { status: 'ping' });
    expect(resp.status).toBe('pong');
  });

  it('单文件索引接口', async () => {
    const resp = (await messenger.request('index/file', {
      action: 'add',
      file: path.resolve(__dirname, '../../../apps/core/code/analyze/context_graph.ts')
    })) as unknown as { status: string };
    console.log(resp, '--------------');
    expect(resp.status).toBe('ok');
  });
  it('开始 build 接口', async () => {
    const resp = (await messenger.request('index/build', {
      repo_dir: path.resolve(__dirname, '../../../')
    })) as unknown as { status: string };
    console.log(resp, '--------------');
    expect(resp.status).toBe('ok');
  });
  it('开始 repoIndex 接口', async () => {
    const resp = (await messenger.request('index/repoIndex', {
      repo_dir: path.resolve(__dirname, '../../../')
    })) as unknown as { status: string };
    console.log(resp, '--------------');
    expect(resp.status).toBe('ok');
  });
  it('开始 clearIndex 接口', async () => {
    const resp = (await messenger.request('index/clearIndex', {
      repo_dir: path.resolve(__dirname, '../../../')
    })) as unknown as { status: string };
    console.log(resp, '--------------');
    expect(resp.status).toBe('ok');
  });
  it('检查仓库索引状态接口', async () => {
    const resp = (await messenger.request('state/checkRepoState', {
      repo_dir: path.resolve(__dirname, '../../../')
    })) as unknown as { id: number };
    console.log(resp, '--------------');
    expect(resp.id).toBe(1);
  });
  it('搜索接口', async () => {
    const resp = (await messenger.request('search/search', {
      query: 'context_graph',
      vercel: {
        nl: 'context_graph'
      },
      topK: 10,
      targetDirectory: [path.resolve(__dirname, '../../../apps/core/code/analyze')]
    })) as unknown as any[];
    console.log(resp, '--------------');
    expect(resp.length).toBeGreaterThan(5);
  });
});
