{"name": "kwaipilot-binary", "version": "1.0.0", "description": "", "bin": "../../dist/out/index.js", "pkg": {"scripts": ["node_modules/axios/**/*"], "assets": ["../../../core/node_modules/sqlite3/**/*", "../../dist/out/tree-sitter.wasm", "../../dist/out/tree-sitter-wasms/*", "../../dist/out/code-snippet-queries/*", "../../dist/out/llamaTokenizer.mjs", "../../dist/out/llamaTokenizerWorkerPool.mjs", "../../dist/out/tiktokenWorkerPool.mjs", "../../dist/out/bin/*"], "targets": ["node18-macos-x64"], "outputPath": "bin"}, "author": "", "license": "Apache-2.0"}